
import React, { useState, useEffect } from "react";
import {  Card, Typography, Grid, CircularProgress, Box, Button, ToggleButton, ToggleButtonGroup, TextField } from "@mui/material";
import { styled } from "@mui/material/styles";
import WbSunnyIcon from "@mui/icons-material/WbSunny";
import CloudIcon from "@mui/icons-material/Cloud";
import UmbrellaIcon from "@mui/icons-material/Umbrella";
import CloudQueueIcon from "@mui/icons-material/CloudQueue";
import AcUnitIcon from "@mui/icons-material/AcUnit";
import RefreshIcon from "@mui/icons-material/Refresh";
import EditLocationIcon from "@mui/icons-material/EditLocation";

interface WeatherData {
  current: {
    temp_c: number;
    temp_f: number;
    humidity: number; // Agregamos la humedad
    condition: {
      text: string;
    };
  };
  forecast: {
    forecastday: Array<{
      day: {
        avgtemp_c: number;
        condition: {
          text: string;
        };
      };
      date: string;
    }>;
  };
  location?: {
    name: string;
    region: string;
    country: string;
  };
}

const StyledContainer = styled(Card)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: "#ffffff",
  borderRadius: "8px",
  border: "1px solid #E5E7EB",
  boxShadow: "2px 2px 0px rgba(31, 142, 235, 0.2)",
  marginBottom: theme.spacing(2),
  transition: "all 0.2s ease",
  "&:hover": {
    transform: "translate(-1px, -1px)",
    boxShadow: "3px 3px 0px rgba(31, 142, 235, 0.3)",
  }
}));

const StyledForecastCard = styled(Card)(({ theme }) => ({
  background: "#F5F5F5",
  color: "#000000",
  minHeight: "120px",
  padding: theme.spacing(1),
  borderRadius: "8px",
  border: "1px solid #E5E7EB",
  boxShadow: "1px 1px 0px rgba(31, 142, 235, 0.1)",
  transition: "all 0.2s ease",
  "&:hover": {
    transform: "translate(-1px, -1px)",
    boxShadow: "2px 2px 0px rgba(31, 142, 235, 0.2)",
  },
  display: "flex",
  flexDirection: "column",
  margin: theme.spacing(0, 1),
}));

const ControlContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  marginBottom: theme.spacing(3),
  flexWrap: "wrap",
  gap: theme.spacing(2),
}));

const getWeatherIcon = (condition: string) => {
  const conditionLower = condition.toLowerCase();
  
  if (conditionLower.includes("sol") || conditionLower.includes("despejado")) {
    return <WbSunnyIcon sx={{ fontSize: 40, color: "#FFD700" }} />; // Amarillo dorado para sol
  } else if (conditionLower.includes("lluvia")) {
    return <UmbrellaIcon sx={{ fontSize: 40, color: "#4682B4" }} />; // Azul acero para lluvia
  } else if (conditionLower.includes("nublado") || conditionLower.includes("nuboso")) {
    return <CloudIcon sx={{ fontSize: 40, color: "#808080" }} />; // Gris para nubes
  } else if (conditionLower.includes("niebla")) {
    return <CloudQueueIcon sx={{ fontSize: 40, color: "#B8B8B8" }} />; // Gris claro para niebla
  } else if (conditionLower.includes("nieve")) {
    return <AcUnitIcon sx={{ fontSize: 40, color: "#E0FFFF" }} />; // Celeste claro para nieve
  }
  return <WbSunnyIcon sx={{ fontSize: 40, color: "#FFD700" }} />; // Por defecto
};

const capitalizeDayName = (date: string) => {
  const dayName = new Date(date).toLocaleDateString("es-ES", {
    weekday: "long",
  });
  return dayName.charAt(0).toUpperCase() + dayName.slice(1);
};

const WeatherDashboard = () => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tempUnit, setTempUnit] = useState<"c" | "f">("c");
  // Eliminamos estos estados ya que no los necesitaremos más
  // const [manualLocation, setManualLocation] = useState<string>("");
  // const [showLocationInput, setShowLocationInput] = useState<boolean>(false);

  // API key de WeatherAPI.com
  const API_KEY = "a80d2077f58948f8ac7193110250804";

  const fetchWeatherData = async (latitude: number, longitude: number) => {
    setLoading(true);
    try {
      if (!isValidCoordinate(latitude, longitude)) {
        throw new Error("Coordenadas inválidas");
      }

      const url = `https://api.weatherapi.com/v1/forecast.json?key=${API_KEY}&q=${latitude},${longitude}&days=7&lang=es`;
      console.log("URL de la API:", url);
      const response = await fetch(url);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Error del servidor: ${response.status}`);
      }

      const data = await response.json();
      
    
      setWeatherData(data);
      setLoading(false);
      setError(null);
    } catch (err) {
      console.error("Error en fetchWeatherData:", err);
      setError(err instanceof Error ? err.message : "Error al cargar los datos del clima");
      setLoading(false);
    }
  };

  const fetchWeatherDataByCity = async (city: string) => {
    setLoading(true);
    try {
      const url = `https://api.weatherapi.com/v1/forecast.json?key=${API_KEY}&q=${encodeURIComponent(city)}&days=7&lang=es`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Error del servidor: ${response.status}`);
      }

      const data = await response.json();
      setWeatherData(data);
      setLoading(false);
      setError(null);
      // Guardar la ubicación en localStorage
      localStorage.setItem('lastKnownLocation', city);
    } catch (err) {
      setError("No se encontró la ubicación. Verifica el nombre ingresado.");
      setLoading(false);
    }
  };

 

  // Función para validar coordenadas
  const isValidCoordinate = (lat: number, lon: number) => {
    return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
  };

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000; // 2 segundos

  const getGeolocationAndFetch = (retryCount = 0) => {
    console.log(`Intento ${retryCount + 1} de ${MAX_RETRIES} para obtener ubicación`);
    
    if (navigator.geolocation) {
      const options = {
        enableHighAccuracy: true,
        timeout: 30000,        // Aumentado a 30 segundos
        maximumAge: 0         // No usar cache
      };

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          console.log("Coordenadas obtenidas:", { 
            latitude, 
            longitude,
            accuracy,
            timestamp: new Date(position.timestamp).toISOString()
          });

          // Solo reintentamos si la precisión es realmente mala (más de 20km)
          if (accuracy > 20000 && retryCount < MAX_RETRIES) {
            console.log(`Precisión insuficiente (${accuracy}m), reintentando...`);
            setLoading(true);
            setTimeout(() => getGeolocationAndFetch(retryCount + 1), RETRY_DELAY);
            return;
          }

          // Usar las coordenadas GPS sin importar la precisión si es el último intento
          if (isValidCoordinate(latitude, longitude)) {
            const lat = Number(latitude.toFixed(4));
            const lon = Number(longitude.toFixed(4));
            fetchWeatherData(lat, lon);
          } else {
            setError("Coordenadas de ubicación inválidas");
            setLoading(false);
          }
        },
        async (err: GeolocationPositionError) => {
          console.error("Error de geolocalización:", {
            code: err.code,
            message: err.message
          });

          // Solo usar IP como último recurso después de varios intentos fallidos
          if (retryCount < MAX_RETRIES) {
            console.log(`Reintentando obtener GPS... Intento ${retryCount + 1}`);
            setTimeout(() => getGeolocationAndFetch(retryCount + 1), RETRY_DELAY);
            return;
          }

          // Solo usamos IP como último recurso
          try {
            const ipResponse = await fetch('https://ipapi.co/json/');
            const ipData = await ipResponse.json();
            
            if (ipData.latitude && ipData.longitude) {
              console.log("Usando ubicación por IP como último recurso");
              fetchWeatherData(ipData.latitude, ipData.longitude);
              return;
            }
          } catch (error) {
            console.error("Error al obtener ubicación por IP:", error);
          }
          
          let errorMessage = "Error al obtener la ubicación. Por favor: \n";
          errorMessage += "1. Verifica que el GPS esté activado\n";
          errorMessage += "2. Permite el acceso a la ubicación en tu navegador\n";
          errorMessage += "3. Asegúrate de tener buena señal GPS y conexión a internet";
          
          setError(errorMessage);
          setLoading(false);
        },
        options
      );
    } else {
      setError("La geolocalización no es soportada por este navegador.");
      setLoading(false);
    }
  };

  useEffect(() => {
    let mounted = true;

    const initializeWeather = async () => {
      if (mounted) {
        // Intentar usar la última ubicación conocida
        const lastLocation = localStorage.getItem('lastKnownLocation');
        if (lastLocation) {
          await fetchWeatherDataByCity(lastLocation);
        } else {
          await getGeolocationAndFetch();
        }
      }
    };

    initializeWeather();

    const interval = setInterval(() => {
      if (mounted) {
        const lastLocation = localStorage.getItem('lastKnownLocation');
        if (lastLocation) {
          fetchWeatherDataByCity(lastLocation);
        } else {
          getGeolocationAndFetch();
        }
      }
    }, 5 * 60 * 1000);

    return () => {
      mounted = false;
      clearInterval(interval);
      console.log("Componente desmontado, limpiando recursos");
    };
  }, []);

  const handleRefresh = () => {
    console.log("Se presionó el botón Actualizar"); // Depuración
    getGeolocationAndFetch();
  };

  const handleUnitChange = (
    event: React.MouseEvent<HTMLElement>,
    newUnit: "c" | "f" | null
  ) => {
    if (newUnit !== null) {
      setTempUnit(newUnit);
    }
  };

  const convertTemp = (tempC: number) => {
    if (typeof tempC !== 'number') {
      console.error('Temperatura inválida recibida:', tempC);
      return 0;
    }
    const converted = tempUnit === "c" ? Math.round(tempC) : Math.round((tempC * 9) / 5 + 32);
    console.log(`Convirtiendo temperatura: ${tempC}°C a ${converted}${tempUnit.toUpperCase()}`);
    return converted;
  };

  if (loading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column" 
        alignItems="center" 
        gap={2} 
        minHeight={200} 
        justifyContent="center"
      >
        <CircularProgress sx={{ color: "#2196F3" }} />
        <Typography sx={{ color: "#2196F3" }}>Cargando datos del clima...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <StyledContainer>
        <Typography variant="h6" component="div" color="error">
          {error}
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<RefreshIcon />} 
          sx={{ mt: 2 }} 
          onClick={handleRefresh}
        >
          Intentar de nuevo
        </Button>
      </StyledContainer>
    );
  }

  if (!weatherData) {
    return null;
  }

  const { current, forecast } = weatherData;
  const unitSymbol = tempUnit === "c" ? "°C" : "°F";

  return (
    <StyledContainer elevation={3}>
      <ControlContainer>
        <Button 
          variant="contained" 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
          sx={{
            backgroundColor: "#2196F3",
            "&:hover": {
              backgroundColor: "#1976D2",
            },
          }}
        >
          Actualizar
        </Button>
        <ToggleButtonGroup
          value={tempUnit}
          exclusive
          onChange={handleUnitChange}
          aria-label="Seleccionar unidad de temperatura"
        >
          <ToggleButton value="c" aria-label="Celsius">
            °C
          </ToggleButton>
          <ToggleButton value="f" aria-label="Fahrenheit">
            °F
          </ToggleButton>
        </ToggleButtonGroup>
        {/* Eliminamos el botón de cambiar ubicación y el formulario asociado */}
      </ControlContainer>

      {/* Eliminamos la sección del formulario de ubicación manual */}

      <Box mb={4}>
        <Typography 
          variant="h6" 
          gutterBottom 
          sx={{ 
            fontFamily: "Lexend, sans-serif",
            fontWeight: "bold", 
            color: "#000000" 
          }}
        >
          Clima Actual
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          {getWeatherIcon(current.condition.text)}
          <Box>
            <Typography 
              variant="h2" 
              sx={{ 
                fontFamily: "Lexend, sans-serif",
                fontWeight: "bold", 
                color: "#000000" 
              }}
            >
              {convertTemp(current.temp_c)}{unitSymbol}
            </Typography>
            <Typography 
              variant="h6" 
              sx={{ 
                fontFamily: "Inter, sans-serif",
                color: "#666666" 
              }}
            >
              {current.condition.text}
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                fontFamily: "Inter, sans-serif",
                color: "#666666",
                mt: 0.5
              }}
            >
              Humedad: {current.humidity}%
            </Typography>
          </Box>
        </Box>
      </Box>

      <Typography 
        variant="h6" 
        gutterBottom 
        sx={{ 
          fontFamily: "Lexend, sans-serif",
          fontWeight: "bold", 
          mb: 2, 
          color: "#000000" 
        }}
      >
        Próximos 2 días
      </Typography>
      <Grid 
        container 
        spacing={3} 
        sx={{ margin: (theme) => theme.spacing(-1, -1), width: "calc(100% + 16px)" }}
      >
        {forecast.forecastday.slice(1).map((day, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <StyledForecastCard>
              <Typography 
                variant="h6" 
                sx={{ 
                  fontFamily: "Lexend, sans-serif",
                  fontWeight: "bold", 
                  color: "#000000",
                  fontSize: "0.9rem",
                  mb: 0.5
                }}
              >
                {capitalizeDayName(day.date)}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} my={0.25}>
                {getWeatherIcon(day.day.condition.text)}
                <Typography 
                  variant="h4" 
                  sx={{ 
                    fontFamily: "Lexend, sans-serif",
                    fontWeight: "bold", 
                    color: "#000000",
                    fontSize: "1.5rem"
                  }}
                >
                  {convertTemp(day.day.avgtemp_c)}{unitSymbol}
                </Typography>
              </Box>
              <Typography 
                sx={{ 
                  fontFamily: "Inter, sans-serif",
                  color: "#666666",
                  fontSize: "0.8rem",
                  mt: "auto"
                }}
              >
                {day.day.condition.text}
              </Typography>
            </StyledForecastCard>
          </Grid>
        ))}
      </Grid>
    </StyledContainer>
  );
};

export default WeatherDashboard;




























