"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/establecimiento/page",{

/***/ "(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx":
/*!*************************************************!*\
  !*** ./src/app/components/mapbox/MapDialog.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mapbox-gl */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.js\");\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mapbox_gl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var mapbox_gl_dist_mapbox_gl_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mapbox-gl/dist/mapbox-gl.css */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.css\");\n/* harmony import */ var _mapbox_mapbox_gl_draw_dist_mapbox_gl_draw_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css */ \"(app-pages-browser)/./node_modules/@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _turf_turf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @turf/turf */ \"(app-pages-browser)/./node_modules/@turf/area/dist/esm/index.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/Map */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Map.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst styles = \"\\n  .mapboxgl-ctrl-custom {\\n    background: #fff;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    height: 29px;\\n    width: 29px;\\n    padding: 0;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .mapboxgl-ctrl-custom:hover {\\n    background-color: #f2f2f2;\\n  }\\n\";\nconst MapDialog = (param)=>{\n    let { openDialog, onClose, onSave, establecimientoId, lotes = [], selectedLoteId = null } = param;\n    _s();\n    const [isMaximized, setIsMaximized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mapStyle, setMapStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mapbox://styles/mapbox/streets-v11\");\n    const mapContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [points, setPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [areaInHectares, setAreaInHectares] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allLotes, setAllLotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isParcelas, setIsParcelas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nombre, setNombre] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Al inicio del componente MapDialog, añade este log\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            // Inspeccionar el formato de las coordenadas\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                    }\n                }[\"MapDialog.useEffect\"]);\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        lotes\n    ]);\n    // Calcula el área del polígono usando turf y actualiza el estado\n    const obtenrDatosSuperficie = ()=>{\n        if (points.length === 4) {\n            const polygonCoords = points.map((p)=>[\n                    p.longitude,\n                    p.latitude\n                ]);\n            // Cerrar el polígono agregando el primer punto al final\n            polygonCoords.push(polygonCoords[0]);\n            const polygonGeoJSON = {\n                type: \"Feature\",\n                properties: {},\n                geometry: {\n                    type: \"Polygon\",\n                    coordinates: [\n                        polygonCoords\n                    ]\n                }\n            };\n            const areaInSquareMeters = _turf_turf__WEBPACK_IMPORTED_MODULE_5__.area(polygonGeoJSON);\n            const areaHectaresCalc = areaInSquareMeters / 10000;\n            setAreaInHectares(areaHectaresCalc.toFixed(2));\n        } else {\n            setAreaInHectares(\"\");\n        }\n    };\n    // Captura el clic en el mapa y añade el punto (máximo 4)\n    const handleMapClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MapDialog.useCallback[handleMapClick]\": (e)=>{\n            setPoints({\n                \"MapDialog.useCallback[handleMapClick]\": (prevPoints)=>{\n                    if (prevPoints.length >= 4) return prevPoints;\n                    return [\n                        ...prevPoints,\n                        {\n                            longitude: e.lngLat.lng,\n                            latitude: e.lngLat.lat\n                        }\n                    ];\n                }\n            }[\"MapDialog.useCallback[handleMapClick]\"]);\n        }\n    }[\"MapDialog.useCallback[handleMapClick]\"], []);\n    // Función para encontrar la ubicación actual\n    const handleFindLocation = ()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const { latitude, longitude } = position.coords;\n                if (mapRef.current) {\n                    mapRef.current.flyTo({\n                        center: [\n                            longitude,\n                            latitude\n                        ],\n                        zoom: 14\n                    });\n                }\n            });\n        }\n    };\n    // Función para eliminar puntos (resetear)\n    const handleDelete = ()=>{\n        setPoints([]);\n    };\n    // Función para cambiar el estilo del mapa\n    const toggleMapStyle = ()=>{\n        const mapStyles = [\n            {\n                value: \"mapbox://styles/mapbox/streets-v11\",\n                label: \"Calles\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/satellite-v9\",\n                label: \"Satélite\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/light-v10\",\n                label: \"Luz\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/dark-v10\",\n                label: \"Oscuro\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/outdoors-v11\",\n                label: \"Afuera\"\n            }\n        ];\n        const currentIndex = mapStyles.findIndex((style)=>style.value === mapStyle);\n        const nextIndex = (currentIndex + 1) % mapStyles.length;\n        setMapStyle(mapStyles[nextIndex].value);\n    };\n    const mapStyles = [\n        {\n            value: \"mapbox://styles/mapbox/streets-v11\",\n            label: \"Calles\",\n            icon: \"🗺️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/satellite-v9\",\n            label: \"Satélite\",\n            icon: \"🛰️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/light-v10\",\n            label: \"Luz\",\n            icon: \"☀️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/dark-v10\",\n            label: \"Oscuro\",\n            icon: \"🌙\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/outdoors-v11\",\n            label: \"Afuera\",\n            icon: \"🏞️\"\n        }\n    ];\n    // Función para inicializar el mapa (extraída para evitar duplicación)\n    const initializeMap = (center)=>{\n        console.log(\"Inicializando mapa en:\", center);\n        const map = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Map)({\n            container: mapContainerRef.current,\n            style: mapStyle,\n            center: center,\n            zoom: 14,\n            projection: \"mercator\"\n        });\n        mapRef.current = map;\n        // Agrega el listener para capturar clics\n        map.on(\"click\", handleMapClick);\n        map.on(\"load\", ()=>{\n            console.log(\"Mapa cargado en coordenadas:\", center);\n            // Agregar controles básicos\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().NavigationControl)(), \"top-right\");\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().FullscreenControl)(), \"top-left\");\n            // Añadir un marcador en la ubicación actual\n            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                color: \"#FF0000\"\n            }).setLngLat(center).addTo(map);\n            // Agregar botón personalizado para ir a Suardi\n            class SuardiButton {\n                onAdd(map) {\n                    this._map = map;\n                    this._container = document.createElement(\"div\");\n                    this._container.className = \"mapboxgl-ctrl mapboxgl-ctrl-group\";\n                    const button = document.createElement(\"button\");\n                    button.className = \"mapboxgl-ctrl-custom\";\n                    button.type = \"button\";\n                    button.title = \"Ir a Suardi\";\n                    button.innerHTML = '<span style=\"font-size: 18px;\">🏠</span>'; // Emoji de casa\n                    button.onclick = ()=>{\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    };\n                    this._container.appendChild(button);\n                    return this._container;\n                }\n                onRemove() {\n                    if (this._container && this._container.parentNode) {\n                        this._container.parentNode.removeChild(this._container);\n                    }\n                }\n            }\n            // Agregar el botón personalizado al mapa\n            map.addControl(new SuardiButton(), \"top-right\");\n            // Dibujar lotes existentes\n            if (lotes && Array.isArray(lotes) && lotes.length > 0) {\n                console.log(\"Dibujando lotes existentes:\", lotes);\n                lotes.forEach((lote, index)=>{\n                    if (lote.coordinates) {\n                        console.log(\"Dibujando lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), lote.coordinates);\n                        // Parse coordinates if they're a string\n                        let coordinatesArray = lote.coordinates;\n                        if (!Array.isArray(coordinatesArray)) {\n                            try {\n                                // Try to parse if it's a JSON string\n                                coordinatesArray = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                console.log(\"Coordenadas parseadas:\", coordinatesArray);\n                            } catch (e) {\n                                console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                return; // Skip this lote if coordinates can't be parsed\n                            }\n                        }\n                        // Ensure coordinates is an array\n                        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n                            console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                            return;\n                        }\n                        // Create a unique ID for each source and layer\n                        const sourceId = \"lote-source-\".concat(lote.id);\n                        const fillLayerId = \"lote-fill-\".concat(lote.id);\n                        const lineLayerId = \"lote-line-\".concat(lote.id);\n                        try {\n                            // Verify and correct the format of coordinates if necessary\n                            // GeoJSON expects coordinates in [longitude, latitude] format\n                            const correctedCoordinates = coordinatesArray.map((coord)=>{\n                                // If coordinates appear to be in [latitude, longitude] format\n                                if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                                    console.log(\"Invirtiendo coordenadas:\", coord, \"->\", [\n                                        coord[1],\n                                        coord[0]\n                                    ]);\n                                    return [\n                                        coord[1],\n                                        coord[0]\n                                    ]; // Invert to [longitude, latitude]\n                                }\n                                return coord; // Already in [longitude, latitude] format\n                            });\n                            // Verify that there are at least 3 points to form a polygon\n                            if (correctedCoordinates.length < 3) {\n                                console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene suficientes puntos para formar un pol\\xedgono\"));\n                                return;\n                            }\n                            // Create a GeoJSON for the lot\n                            const loteGeoJSON = {\n                                type: \"Feature\",\n                                properties: {\n                                    id: lote.id,\n                                    name: lote.name,\n                                    area: lote.area\n                                },\n                                geometry: {\n                                    type: \"Polygon\",\n                                    coordinates: [\n                                        correctedCoordinates\n                                    ]\n                                }\n                            };\n                            // Add the source to the map\n                            map.addSource(sourceId, {\n                                type: \"geojson\",\n                                data: loteGeoJSON\n                            });\n                            // Obtener el color para este establecimiento\n                            const establecimientoColor = getColorForEstablecimiento(establecimientoId);\n                            // Añadir fill layer con el color del establecimiento\n                            map.addLayer({\n                                id: fillLayerId,\n                                type: \"fill\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"fill-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff9900\",\n                                        establecimientoColor\n                                    ],\n                                    \"fill-opacity\": 0.5\n                                }\n                            });\n                            // Añadir esta propiedad al mapa para mejorar la proyección\n                            map.addSource(\"mapbox-dem\", {\n                                type: \"raster-dem\",\n                                url: \"mapbox://mapbox.mapbox-terrain-dem-v1\",\n                                tileSize: 512,\n                                maxzoom: 14\n                            });\n                            map.setTerrain({\n                                source: \"mapbox-dem\",\n                                exaggeration: 1.0\n                            });\n                            // Añadir line layer con el color del establecimiento\n                            map.addLayer({\n                                id: lineLayerId,\n                                type: \"line\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"line-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff6600\",\n                                        establecimientoColor\n                                    ],\n                                    \"line-width\": 2\n                                }\n                            });\n                            // Add label with lot name\n                            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                                element: createLabelElement(lote.name, lote.area),\n                                anchor: \"center\"\n                            }).setLngLat(getCentroid(lote.coordinates)).addTo(map);\n                        } catch (error) {\n                            console.error(\"Error al dibujar el lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), error);\n                        }\n                    } else {\n                        console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas para dibujar\"));\n                    }\n                });\n                // If there are lots, adjust the view to show them all\n                if (lotes.length > 0) {\n                    try {\n                        // Create a bounds that includes all lots\n                        const bounds = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().LngLatBounds)();\n                        let hasValidCoordinates = false;\n                        lotes.forEach((lote)=>{\n                            // Get coordinates and parse if needed\n                            let loteCoordinates = lote.coordinates;\n                            if (!Array.isArray(loteCoordinates)) {\n                                try {\n                                    loteCoordinates = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                } catch (e) {\n                                    console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                    return; // Skip this lote\n                                }\n                            }\n                            if (Array.isArray(loteCoordinates) && loteCoordinates.length > 0) {\n                                loteCoordinates.forEach((coord)=>{\n                                    // Verify that the coordinates are valid and close to Suardi\n                                    const correctedCoord = Math.abs(coord[0]) <= 90 ? [\n                                        coord[1],\n                                        coord[0]\n                                    ] : coord;\n                                    // Verify that the coordinates are within a reasonable range near Suardi\n                                    const suardiLat = -30.5351;\n                                    const suardiLng = -61.9625;\n                                    const maxDistance = 2; // Maximum degrees of distance (approximately 200km)\n                                    if (Math.abs(correctedCoord[1] - suardiLat) < maxDistance && Math.abs(correctedCoord[0] - suardiLng) < maxDistance) {\n                                        bounds.extend(correctedCoord);\n                                        hasValidCoordinates = true;\n                                    } else {\n                                        console.warn(\"Coordenada ignorada por estar demasiado lejos de Suardi:\", correctedCoord);\n                                    }\n                                });\n                            }\n                        });\n                        // Adjust the map to show all valid lots\n                        if (hasValidCoordinates) {\n                            map.fitBounds(bounds, {\n                                padding: 50,\n                                maxZoom: 15\n                            });\n                        } else {\n                            // If no valid coordinates, center on Suardi\n                            const suardiCoords = [\n                                -61.9625,\n                                -30.5351\n                            ];\n                            map.flyTo({\n                                center: suardiCoords,\n                                zoom: 14,\n                                essential: true\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error al ajustar la vista del mapa:\", error);\n                        // In case of error, center on Suardi\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ];\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    }\n                } else {\n                    // If no lots, center on Suardi\n                    const suardiCoords = [\n                        -61.9625,\n                        -30.5351\n                    ];\n                    map.flyTo({\n                        center: suardiCoords,\n                        zoom: 14,\n                        essential: true\n                    });\n                }\n            }\n        });\n    };\n    // Función auxiliar para crear un elemento de etiqueta\n    const createLabelElement = (name, area)=>{\n        const el = document.createElement(\"div\");\n        el.className = \"lote-label\";\n        el.style.backgroundColor = \"white\";\n        el.style.border = \"1px solid \".concat(getColorForEstablecimiento(establecimientoId));\n        el.style.borderRadius = \"4px\";\n        el.style.padding = \"4px 8px\";\n        el.style.fontSize = \"12px\";\n        el.style.fontWeight = \"bold\";\n        el.style.whiteSpace = \"nowrap\";\n        el.style.pointerEvents = \"none\";\n        el.innerHTML = \"\".concat(name, \" (\").concat(area.toFixed(2), \" ha)\");\n        return el;\n    };\n    // Función auxiliar para obtener el centroide de un polígono\n    const getCentroid = (coordinates)=>{\n        // Parse coordinates if they're a string\n        let coordinatesArray = coordinates;\n        if (!Array.isArray(coordinatesArray)) {\n            try {\n                coordinatesArray = JSON.parse(coordinates);\n            } catch (e) {\n                console.error(\"Error al parsear coordenadas para centroide:\", e);\n                return [\n                    -61.9625,\n                    -30.5351\n                ]; // Default to Suardi coordinates\n            }\n        }\n        // Ensure coordinates is an array\n        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n            return [\n                -61.9625,\n                -30.5351\n            ]; // Default to Suardi coordinates\n        }\n        // Verificar si las coordenadas están en formato [latitud, longitud]\n        const correctedCoords = coordinatesArray.map((coord)=>{\n            if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                return [\n                    coord[1],\n                    coord[0]\n                ]; // Convertir a [longitud, latitud]\n            }\n            return coord;\n        });\n        let sumX = 0;\n        let sumY = 0;\n        correctedCoords.forEach((coord)=>{\n            sumX += coord[0];\n            sumY += coord[1];\n        });\n        return [\n            sumX / correctedCoords.length,\n            sumY / correctedCoords.length\n        ];\n    };\n    // Inicializa el mapa cuando se abre el diálogo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!openDialog) return;\n            // Reinicia datos al abrir el diálogo\n            setPoints([]);\n            setAreaInHectares(\"\");\n            // Depuración de lotes\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                        // Verificar si las coordenadas son válidas\n                        if (!lote.coordinates || !Array.isArray(lote.coordinates) || lote.coordinates.length === 0) {\n                            console.warn(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                        }\n                    }\n                }[\"MapDialog.useEffect\"]);\n            } else {\n                console.log(\"No hay lotes para mostrar en el mapa\");\n            }\n            const timer = setTimeout({\n                \"MapDialog.useEffect.timer\": ()=>{\n                    if (mapContainerRef.current && !mapRef.current) {\n                        (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().accessToken) = \"pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A\";\n                        // Inicializar directamente en Suardi, Santa Fe\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        console.log(\"Inicializando mapa en Suardi, Santa Fe\");\n                        initializeMap(suardiCoords);\n                    }\n                }\n            }[\"MapDialog.useEffect.timer\"], 500);\n            return ({\n                \"MapDialog.useEffect\": ()=>{\n                    if (mapRef.current) {\n                        mapRef.current.remove();\n                        mapRef.current = null;\n                    }\n                    clearTimeout(timer);\n                }\n            })[\"MapDialog.useEffect\"];\n        }\n    }[\"MapDialog.useEffect\"], [\n        openDialog,\n        mapStyle,\n        lotes,\n        handleMapClick,\n        establecimientoId\n    ]);\n    // Actualiza marcadores y polígono cuando se actualicen los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!mapRef.current) return;\n            // Properly type the markers array\n            markersRef.current.forEach({\n                \"MapDialog.useEffect\": (marker)=>marker.remove()\n            }[\"MapDialog.useEffect\"]);\n            markersRef.current = [];\n            // Create a marker for each point\n            points.forEach({\n                \"MapDialog.useEffect\": (p)=>{\n                    const marker = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                        scale: 0.6\n                    }).setLngLat([\n                        p.longitude,\n                        p.latitude\n                    ]).addTo(mapRef.current); // Use non-null assertion since we checked above\n                    markersRef.current.push(marker);\n                }\n            }[\"MapDialog.useEffect\"]);\n            // If there are 4 points, draw the polygon\n            if (points.length === 4) {\n                const polygonCoords = points.map({\n                    \"MapDialog.useEffect.polygonCoords\": (p)=>[\n                            p.longitude,\n                            p.latitude\n                        ]\n                }[\"MapDialog.useEffect.polygonCoords\"]);\n                polygonCoords.push(polygonCoords[0]); // Close the polygon\n                const polygonGeoJSON = {\n                    type: \"Feature\",\n                    properties: {},\n                    geometry: {\n                        type: \"Polygon\",\n                        coordinates: [\n                            polygonCoords\n                        ]\n                    }\n                };\n                if (mapRef.current.getSource(\"polygon\")) {\n                    const source = mapRef.current.getSource(\"polygon\");\n                    source.setData(polygonGeoJSON);\n                } else {\n                    mapRef.current.addSource(\"polygon\", {\n                        type: \"geojson\",\n                        data: polygonGeoJSON\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-fill\",\n                        type: \"fill\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"fill-color\": \"#0080ff\",\n                            \"fill-opacity\": 0.5\n                        }\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-outline\",\n                        type: \"line\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"line-color\": \"#0080ff\",\n                            \"line-width\": 2\n                        }\n                    });\n                }\n            } else {\n                // Remove polygon if it exists\n                if (mapRef.current.getLayer(\"polygon-fill\")) {\n                    mapRef.current.removeLayer(\"polygon-fill\");\n                }\n                if (mapRef.current.getLayer(\"polygon-outline\")) {\n                    mapRef.current.removeLayer(\"polygon-outline\");\n                }\n                if (mapRef.current.getSource(\"polygon\")) {\n                    mapRef.current.removeSource(\"polygon\");\n                }\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Recalcula el área cada vez que se actualizan los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            obtenrDatosSuperficie();\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Genera el string con todas las coordenadas, separándolas con \" | \"\n    const coordinatesString = points.map((p)=>\"\".concat(p.longitude.toFixed(6), \", \").concat(p.latitude.toFixed(6))).join(\" | \");\n    // Guarda los datos y resetea el estado\n    const handleLoadValues = ()=>{\n        if (points.length >= 4) {\n            obtenrDatosSuperficie();\n            const data = {\n                points: points.map((p)=>({\n                        latitude: p.latitude,\n                        longitude: p.longitude\n                    })),\n                areaInHectares: Number(areaInHectares),\n                name: nombre\n            };\n            onSave(data); // Pass data to parent component without showing alert\n            onClose();\n        } else {\n            // Optional: Add error handling for incomplete polygon\n            alert(\"Por favor, marque 4 puntos en el mapa para definir el lote.\");\n        }\n    };\n    // Añade un manejador de cambios para el TextField\n    const handleNameChange = (e)=>{\n        setNombre(e.target.value);\n    };\n    // Añade esta función para generar colores basados en el ID del establecimiento\n    const getColorForEstablecimiento = (establecimientoId)=>{\n        // Lista de colores distintos para diferentes establecimientos\n        const colors = [\n            \"#3498db\",\n            \"#e74c3c\",\n            \"#2ecc71\",\n            \"#f39c12\",\n            \"#9b59b6\",\n            \"#1abc9c\",\n            \"#d35400\",\n            \"#27ae60\",\n            \"#c0392b\",\n            \"#8e44ad\"\n        ];\n        // Convertir el ID a un número para usarlo como índice\n        let numericId = 0;\n        if (establecimientoId) {\n            // Si es un UUID, usar la suma de los códigos de caracteres\n            if (establecimientoId.includes(\"-\")) {\n                numericId = establecimientoId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n            } else {\n                // Si es numérico, convertirlo\n                numericId = parseInt(establecimientoId, 10) || 0;\n            }\n        }\n        // Usar el módulo para obtener un índice dentro del rango de colores\n        const colorIndex = numericId % colors.length;\n        return colors[colorIndex];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        open: openDialog,\n        onClose: onClose,\n        maxWidth: \"md\",\n        fullWidth: true,\n        sx: {\n            \"& .MuiPaper-root\": {\n                transition: \"width 0.3s ease, height 0.3s ease\",\n                width: isMaximized ? \"100%\" : \"auto\",\n                height: isMaximized ? \"100%\" : \"auto\",\n                maxWidth: isMaximized ? \"100%\" : \"900px\",\n                maxHeight: isMaximized ? \"100%\" : \"800px\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: [\n                    \"Marcar Coordenadas\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        \"aria-label\": \"close\",\n                        onClick: onClose,\n                        sx: {\n                            position: \"absolute\",\n                            right: 8,\n                            top: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 793,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        sx: {\n                            backgroundColor: \"#f0f4f8\",\n                            padding: \"20px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 6px rgba(0,0,0,0.1)\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            marginTop: \"15px\",\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    color: \"#5C6BC0\",\n                                    fontSize: \"30px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    color: \"#555\",\n                                    fontSize: \"16px\",\n                                    fontWeight: 400\n                                },\n                                children: \"Marque 4 puntos en el mapa para definir el lote y/o parcela. Ingrese un nombre y el sistema calcular\\xe1 autom\\xe1ticamente el \\xe1rea. Use el bot\\xf3n superior derecho para cambiar la vista.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: mapContainerRef,\n                        style: {\n                            position: \"relative\",\n                            height: \"500px\",\n                            border: \"2px solid black\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        mt: 2,\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: 2,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                label: \"Nombre del Lote\",\n                                variant: \"outlined\",\n                                value: nombre,\n                                onChange: handleNameChange,\n                                InputProps: {\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    }\n                                },\n                                sx: {\n                                    width: \"300px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                display: \"flex\",\n                                gap: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Coordenadas\",\n                                        value: coordinatesString,\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 3\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Superficie (ha)\",\n                                        value: areaInHectares ? \"\".concat(areaInHectares, \" ha\") : \"\",\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 802,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    onClick: handleLoadValues,\n                    color: \"primary\",\n                    children: \"Cargar Valores\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                    lineNumber: 867,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 866,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n        lineNumber: 776,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MapDialog, \"Et7s0ulCz9xzg3s0PVQqtquM0x4=\");\n_c = MapDialog;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapDialog);\nvar _c;\n$RefreshReg$(_c, \"MapDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx\n"));

/***/ })

});