"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/establecimiento/page",{

/***/ "(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx":
/*!*************************************************!*\
  !*** ./src/app/components/mapbox/MapDialog.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mapbox-gl */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.js\");\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mapbox_gl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var mapbox_gl_dist_mapbox_gl_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mapbox-gl/dist/mapbox-gl.css */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.css\");\n/* harmony import */ var _mapbox_mapbox_gl_draw_dist_mapbox_gl_draw_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css */ \"(app-pages-browser)/./node_modules/@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _turf_turf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @turf/turf */ \"(app-pages-browser)/./node_modules/@turf/area/dist/esm/index.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/Map */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Map.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst styles = \"\\n  .mapboxgl-ctrl-custom {\\n    background: #fff;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    height: 29px;\\n    width: 29px;\\n    padding: 0;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .mapboxgl-ctrl-custom:hover {\\n    background-color: #f2f2f2;\\n  }\\n\";\nconst MapDialog = (param)=>{\n    let { openDialog, onClose, onSave, establecimientoId, lotes = [], selectedLoteId = null } = param;\n    _s();\n    const [isMaximized, setIsMaximized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mapStyle, setMapStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mapbox://styles/mapbox/streets-v11\");\n    const mapContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [points, setPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [areaInHectares, setAreaInHectares] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allLotes, setAllLotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isParcelas, setIsParcelas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nombre, setNombre] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Al inicio del componente MapDialog, añade este log\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            // Inspeccionar el formato de las coordenadas\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                    }\n                }[\"MapDialog.useEffect\"]);\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        lotes\n    ]);\n    // Calcula el área del polígono usando turf y actualiza el estado\n    const obtenrDatosSuperficie = ()=>{\n        if (points.length === 4) {\n            const polygonCoords = points.map((p)=>[\n                    p.longitude,\n                    p.latitude\n                ]);\n            // Cerrar el polígono agregando el primer punto al final\n            polygonCoords.push(polygonCoords[0]);\n            const polygonGeoJSON = {\n                type: \"Feature\",\n                properties: {},\n                geometry: {\n                    type: \"Polygon\",\n                    coordinates: [\n                        polygonCoords\n                    ]\n                }\n            };\n            const areaInSquareMeters = _turf_turf__WEBPACK_IMPORTED_MODULE_5__.area(polygonGeoJSON);\n            const areaHectaresCalc = areaInSquareMeters / 10000;\n            setAreaInHectares(areaHectaresCalc.toFixed(2));\n        } else {\n            setAreaInHectares(\"\");\n        }\n    };\n    // Captura el clic en el mapa y añade el punto (máximo 4)\n    const handleMapClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MapDialog.useCallback[handleMapClick]\": (e)=>{\n            setPoints({\n                \"MapDialog.useCallback[handleMapClick]\": (prevPoints)=>{\n                    if (prevPoints.length >= 4) return prevPoints;\n                    return [\n                        ...prevPoints,\n                        {\n                            longitude: e.lngLat.lng,\n                            latitude: e.lngLat.lat\n                        }\n                    ];\n                }\n            }[\"MapDialog.useCallback[handleMapClick]\"]);\n        }\n    }[\"MapDialog.useCallback[handleMapClick]\"], []);\n    // Función para encontrar la ubicación actual\n    const handleFindLocation = ()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const { latitude, longitude } = position.coords;\n                if (mapRef.current) {\n                    mapRef.current.flyTo({\n                        center: [\n                            longitude,\n                            latitude\n                        ],\n                        zoom: 14\n                    });\n                }\n            });\n        }\n    };\n    // Función para eliminar puntos (resetear)\n    const handleDelete = ()=>{\n        setPoints([]);\n    };\n    // Función para cambiar el estilo del mapa\n    const toggleMapStyle = ()=>{\n        const mapStyles = [\n            {\n                value: \"mapbox://styles/mapbox/streets-v11\",\n                label: \"Calles\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/satellite-v9\",\n                label: \"Satélite\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/light-v10\",\n                label: \"Luz\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/dark-v10\",\n                label: \"Oscuro\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/outdoors-v11\",\n                label: \"Afuera\"\n            }\n        ];\n        const currentIndex = mapStyles.findIndex((style)=>style.value === mapStyle);\n        const nextIndex = (currentIndex + 1) % mapStyles.length;\n        setMapStyle(mapStyles[nextIndex].value);\n    };\n    const mapStyles = [\n        {\n            value: \"mapbox://styles/mapbox/streets-v11\",\n            label: \"Calles\",\n            icon: \"🗺️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/satellite-v9\",\n            label: \"Satélite\",\n            icon: \"🛰️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/light-v10\",\n            label: \"Luz\",\n            icon: \"☀️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/dark-v10\",\n            label: \"Oscuro\",\n            icon: \"🌙\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/outdoors-v11\",\n            label: \"Afuera\",\n            icon: \"🏞️\"\n        }\n    ];\n    // Función para inicializar el mapa (extraída para evitar duplicación)\n    const initializeMap = (center)=>{\n        console.log(\"Inicializando mapa en:\", center);\n        const map = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Map)({\n            container: mapContainerRef.current,\n            style: mapStyle,\n            center: center,\n            zoom: 14,\n            projection: \"mercator\"\n        });\n        mapRef.current = map;\n        // Agrega el listener para capturar clics\n        map.on(\"click\", handleMapClick);\n        map.on(\"load\", ()=>{\n            console.log(\"Mapa cargado en coordenadas:\", center);\n            // Agregar controles básicos\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().NavigationControl)(), \"top-right\");\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().FullscreenControl)(), \"top-left\");\n            // Añadir un marcador en la ubicación actual\n            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                color: \"#FF0000\"\n            }).setLngLat(center).addTo(map);\n            // Agregar botón personalizado para ir a Suardi\n            class SuardiButton {\n                onAdd(map) {\n                    this._map = map;\n                    this._container = document.createElement(\"div\");\n                    this._container.className = \"mapboxgl-ctrl mapboxgl-ctrl-group\";\n                    const button = document.createElement(\"button\");\n                    button.className = \"mapboxgl-ctrl-custom\";\n                    button.type = \"button\";\n                    button.title = \"Ir a Suardi\";\n                    button.innerHTML = '<span style=\"font-size: 18px;\">🏠</span>'; // Emoji de casa\n                    button.onclick = ()=>{\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    };\n                    this._container.appendChild(button);\n                    return this._container;\n                }\n                onRemove() {\n                    if (this._container && this._container.parentNode) {\n                        this._container.parentNode.removeChild(this._container);\n                    }\n                }\n            }\n            // Agregar el botón personalizado al mapa\n            map.addControl(new SuardiButton(), \"top-right\");\n            // Dibujar lotes existentes\n            if (lotes && Array.isArray(lotes) && lotes.length > 0) {\n                console.log(\"Dibujando lotes existentes:\", lotes);\n                lotes.forEach((lote, index)=>{\n                    if (lote.coordinates) {\n                        console.log(\"Dibujando lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), lote.coordinates);\n                        // Parse coordinates if they're a string\n                        let coordinatesArray = lote.coordinates;\n                        if (!Array.isArray(coordinatesArray)) {\n                            try {\n                                // Try to parse if it's a JSON string\n                                coordinatesArray = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                console.log(\"Coordenadas parseadas:\", coordinatesArray);\n                            } catch (e) {\n                                console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                return; // Skip this lote if coordinates can't be parsed\n                            }\n                        }\n                        // Ensure coordinates is an array\n                        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n                            console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                            return;\n                        }\n                        // Create a unique ID for each source and layer\n                        const sourceId = \"lote-source-\".concat(lote.id);\n                        const fillLayerId = \"lote-fill-\".concat(lote.id);\n                        const lineLayerId = \"lote-line-\".concat(lote.id);\n                        try {\n                            // Verify and correct the format of coordinates if necessary\n                            // GeoJSON expects coordinates in [longitude, latitude] format\n                            const correctedCoordinates = coordinatesArray.map((coord)=>{\n                                // If coordinates appear to be in [latitude, longitude] format\n                                if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                                    console.log(\"Invirtiendo coordenadas:\", coord, \"->\", [\n                                        coord[1],\n                                        coord[0]\n                                    ]);\n                                    return [\n                                        coord[1],\n                                        coord[0]\n                                    ]; // Invert to [longitude, latitude]\n                                }\n                                return coord; // Already in [longitude, latitude] format\n                            });\n                            // Verify that there are at least 3 points to form a polygon\n                            if (correctedCoordinates.length < 3) {\n                                console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene suficientes puntos para formar un pol\\xedgono\"));\n                                return;\n                            }\n                            // Create a GeoJSON for the lot\n                            const loteGeoJSON = {\n                                type: \"Feature\",\n                                properties: {\n                                    id: lote.id,\n                                    name: lote.name,\n                                    area: lote.area\n                                },\n                                geometry: {\n                                    type: \"Polygon\",\n                                    coordinates: [\n                                        correctedCoordinates\n                                    ]\n                                }\n                            };\n                            // Add the source to the map\n                            map.addSource(sourceId, {\n                                type: \"geojson\",\n                                data: loteGeoJSON\n                            });\n                            // Obtener el color para este establecimiento\n                            const establecimientoColor = getColorForEstablecimiento(establecimientoId);\n                            // Añadir fill layer con el color del establecimiento\n                            map.addLayer({\n                                id: fillLayerId,\n                                type: \"fill\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"fill-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff9900\",\n                                        establecimientoColor\n                                    ],\n                                    \"fill-opacity\": 0.5\n                                }\n                            });\n                            // Añadir esta propiedad al mapa para mejorar la proyección\n                            map.addSource(\"mapbox-dem\", {\n                                type: \"raster-dem\",\n                                url: \"mapbox://mapbox.mapbox-terrain-dem-v1\",\n                                tileSize: 512,\n                                maxzoom: 14\n                            });\n                            map.setTerrain({\n                                source: \"mapbox-dem\",\n                                exaggeration: 1.0\n                            });\n                            // Añadir line layer con el color del establecimiento\n                            map.addLayer({\n                                id: lineLayerId,\n                                type: \"line\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"line-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff6600\",\n                                        establecimientoColor\n                                    ],\n                                    \"line-width\": 2\n                                }\n                            });\n                            // Add label with lot name\n                            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                                element: createLabelElement(lote.name, lote.area),\n                                anchor: \"center\"\n                            }).setLngLat(getCentroid(lote.coordinates)).addTo(map);\n                        } catch (error) {\n                            console.error(\"Error al dibujar el lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), error);\n                        }\n                    } else {\n                        console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas para dibujar\"));\n                    }\n                });\n                // If there are lots, adjust the view to show them all\n                if (lotes.length > 0) {\n                    try {\n                        // Create a bounds that includes all lots\n                        const bounds = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().LngLatBounds)();\n                        let hasValidCoordinates = false;\n                        lotes.forEach((lote)=>{\n                            // Get coordinates and parse if needed\n                            let loteCoordinates = lote.coordinates;\n                            if (!Array.isArray(loteCoordinates)) {\n                                try {\n                                    loteCoordinates = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                } catch (e) {\n                                    console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                    return; // Skip this lote\n                                }\n                            }\n                            if (Array.isArray(loteCoordinates) && loteCoordinates.length > 0) {\n                                loteCoordinates.forEach((coord)=>{\n                                    // Verify that the coordinates are valid and close to Suardi\n                                    const correctedCoord = Math.abs(coord[0]) <= 90 ? [\n                                        coord[1],\n                                        coord[0]\n                                    ] : coord;\n                                    // Verify that the coordinates are within a reasonable range near Suardi\n                                    const suardiLat = -30.5351;\n                                    const suardiLng = -61.9625;\n                                    const maxDistance = 2; // Maximum degrees of distance (approximately 200km)\n                                    if (Math.abs(correctedCoord[1] - suardiLat) < maxDistance && Math.abs(correctedCoord[0] - suardiLng) < maxDistance) {\n                                        bounds.extend(correctedCoord);\n                                        hasValidCoordinates = true;\n                                    } else {\n                                        console.warn(\"Coordenada ignorada por estar demasiado lejos de Suardi:\", correctedCoord);\n                                    }\n                                });\n                            }\n                        });\n                        // Adjust the map to show all valid lots\n                        if (hasValidCoordinates) {\n                            map.fitBounds(bounds, {\n                                padding: 50,\n                                maxZoom: 15\n                            });\n                        } else {\n                            // If no valid coordinates, center on Suardi\n                            const suardiCoords = [\n                                -61.9625,\n                                -30.5351\n                            ];\n                            map.flyTo({\n                                center: suardiCoords,\n                                zoom: 14,\n                                essential: true\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error al ajustar la vista del mapa:\", error);\n                        // In case of error, center on Suardi\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ];\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    }\n                } else {\n                    // If no lots, center on Suardi\n                    const suardiCoords = [\n                        -61.9625,\n                        -30.5351\n                    ];\n                    map.flyTo({\n                        center: suardiCoords,\n                        zoom: 14,\n                        essential: true\n                    });\n                }\n            }\n        });\n    };\n    // Función auxiliar para crear un elemento de etiqueta\n    const createLabelElement = (name, area)=>{\n        const el = document.createElement(\"div\");\n        el.className = \"lote-label\";\n        el.style.backgroundColor = \"white\";\n        el.style.border = \"1px solid \".concat(getColorForEstablecimiento(establecimientoId));\n        el.style.borderRadius = \"4px\";\n        el.style.padding = \"4px 8px\";\n        el.style.fontSize = \"12px\";\n        el.style.fontWeight = \"bold\";\n        el.style.whiteSpace = \"nowrap\";\n        el.style.pointerEvents = \"none\";\n        el.innerHTML = \"\".concat(name, \" (\").concat(area.toFixed(2), \" ha)\");\n        return el;\n    };\n    // Función auxiliar para obtener el centroide de un polígono\n    const getCentroid = (coordinates)=>{\n        // Parse coordinates if they're a string\n        let coordinatesArray = coordinates;\n        if (!Array.isArray(coordinatesArray)) {\n            try {\n                coordinatesArray = JSON.parse(coordinates);\n            } catch (e) {\n                console.error(\"Error al parsear coordenadas para centroide:\", e);\n                return [\n                    -61.9625,\n                    -30.5351\n                ]; // Default to Suardi coordinates\n            }\n        }\n        // Ensure coordinates is an array\n        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n            return [\n                -61.9625,\n                -30.5351\n            ]; // Default to Suardi coordinates\n        }\n        // Verificar si las coordenadas están en formato [latitud, longitud]\n        const correctedCoords = coordinatesArray.map((coord)=>{\n            if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                return [\n                    coord[1],\n                    coord[0]\n                ]; // Convertir a [longitud, latitud]\n            }\n            return coord;\n        });\n        let sumX = 0;\n        let sumY = 0;\n        correctedCoords.forEach((coord)=>{\n            sumX += coord[0];\n            sumY += coord[1];\n        });\n        return [\n            sumX / correctedCoords.length,\n            sumY / correctedCoords.length\n        ];\n    };\n    // Inicializa el mapa cuando se abre el diálogo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!openDialog) return;\n            // Reinicia datos al abrir el diálogo\n            setPoints([]);\n            setAreaInHectares(\"\");\n            // Depuración de lotes\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                        // Verificar si las coordenadas son válidas\n                        if (!lote.coordinates || !Array.isArray(lote.coordinates) || lote.coordinates.length === 0) {\n                            console.warn(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                        }\n                    }\n                }[\"MapDialog.useEffect\"]);\n            } else {\n                console.log(\"No hay lotes para mostrar en el mapa\");\n            }\n            const timer = setTimeout({\n                \"MapDialog.useEffect.timer\": ()=>{\n                    if (mapContainerRef.current && !mapRef.current) {\n                        (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().accessToken) = \"pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A\";\n                        // Inicializar directamente en Suardi, Santa Fe\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        console.log(\"Inicializando mapa en Suardi, Santa Fe\");\n                        initializeMap(suardiCoords);\n                    }\n                }\n            }[\"MapDialog.useEffect.timer\"], 500);\n            return ({\n                \"MapDialog.useEffect\": ()=>{\n                    if (mapRef.current) {\n                        mapRef.current.remove();\n                        mapRef.current = null;\n                    }\n                    clearTimeout(timer);\n                }\n            })[\"MapDialog.useEffect\"];\n        }\n    }[\"MapDialog.useEffect\"], [\n        openDialog,\n        mapStyle,\n        lotes,\n        handleMapClick,\n        establecimientoId\n    ]);\n    // Actualiza marcadores y polígono cuando se actualicen los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!mapRef.current) return;\n            // Properly type the markers array\n            markersRef.current.forEach({\n                \"MapDialog.useEffect\": (marker)=>marker.remove()\n            }[\"MapDialog.useEffect\"]);\n            markersRef.current = [];\n            // Create a marker for each point\n            points.forEach({\n                \"MapDialog.useEffect\": (p)=>{\n                    const marker = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                        scale: 0.6\n                    }).setLngLat([\n                        p.longitude,\n                        p.latitude\n                    ]).addTo(mapRef.current); // Use non-null assertion since we checked above\n                    markersRef.current.push(marker);\n                }\n            }[\"MapDialog.useEffect\"]);\n            // If there are 4 points, draw the polygon\n            if (points.length === 4) {\n                const polygonCoords = points.map({\n                    \"MapDialog.useEffect.polygonCoords\": (p)=>[\n                            p.longitude,\n                            p.latitude\n                        ]\n                }[\"MapDialog.useEffect.polygonCoords\"]);\n                polygonCoords.push(polygonCoords[0]); // Close the polygon\n                const polygonGeoJSON = {\n                    type: \"Feature\",\n                    properties: {},\n                    geometry: {\n                        type: \"Polygon\",\n                        coordinates: [\n                            polygonCoords\n                        ]\n                    }\n                };\n                if (mapRef.current.getSource(\"polygon\")) {\n                    const source = mapRef.current.getSource(\"polygon\");\n                    source.setData(polygonGeoJSON);\n                } else {\n                    mapRef.current.addSource(\"polygon\", {\n                        type: \"geojson\",\n                        data: polygonGeoJSON\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-fill\",\n                        type: \"fill\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"fill-color\": \"#0080ff\",\n                            \"fill-opacity\": 0.5\n                        }\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-outline\",\n                        type: \"line\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"line-color\": \"#0080ff\",\n                            \"line-width\": 2\n                        }\n                    });\n                }\n            } else {\n                // Remove polygon if it exists\n                if (mapRef.current.getLayer(\"polygon-fill\")) {\n                    mapRef.current.removeLayer(\"polygon-fill\");\n                }\n                if (mapRef.current.getLayer(\"polygon-outline\")) {\n                    mapRef.current.removeLayer(\"polygon-outline\");\n                }\n                if (mapRef.current.getSource(\"polygon\")) {\n                    mapRef.current.removeSource(\"polygon\");\n                }\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Recalcula el área cada vez que se actualizan los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            obtenrDatosSuperficie();\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Genera el string con todas las coordenadas, separándolas con \" | \"\n    const coordinatesString = points.map((p)=>\"\".concat(p.longitude.toFixed(6), \", \").concat(p.latitude.toFixed(6))).join(\" | \");\n    // Guarda los datos y resetea el estado\n    const handleLoadValues = ()=>{\n        if (points.length >= 4) {\n            obtenrDatosSuperficie();\n            const data = {\n                points: points.map((p)=>({\n                        latitude: p.latitude,\n                        longitude: p.longitude\n                    })),\n                areaInHectares: Number(areaInHectares),\n                name: nombre\n            };\n            onSave(data); // Pass data to parent component without showing alert\n            onClose();\n        } else {\n            // Optional: Add error handling for incomplete polygon\n            alert(\"Por favor, marque 4 puntos en el mapa para definir el lote.\");\n        }\n    };\n    // Añade un manejador de cambios para el TextField\n    const handleNameChange = (e)=>{\n        setNombre(e.target.value);\n    };\n    // Añade esta función para generar colores basados en el ID del establecimiento\n    const getColorForEstablecimiento = (establecimientoId)=>{\n        // Lista de colores distintos para diferentes establecimientos\n        const colors = [\n            \"#3498db\",\n            \"#e74c3c\",\n            \"#2ecc71\",\n            \"#f39c12\",\n            \"#9b59b6\",\n            \"#1abc9c\",\n            \"#d35400\",\n            \"#27ae60\",\n            \"#c0392b\",\n            \"#8e44ad\"\n        ];\n        // Convertir el ID a un número para usarlo como índice\n        let numericId = 0;\n        if (establecimientoId) {\n            // Si es un UUID, usar la suma de los códigos de caracteres\n            if (establecimientoId.includes(\"-\")) {\n                numericId = establecimientoId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n            } else {\n                // Si es numérico, convertirlo\n                numericId = parseInt(establecimientoId, 10) || 0;\n            }\n        }\n        // Usar el módulo para obtener un índice dentro del rango de colores\n        const colorIndex = numericId % colors.length;\n        return colors[colorIndex];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        open: openDialog,\n        onClose: onClose,\n        maxWidth: \"md\",\n        fullWidth: true,\n        sx: {\n            \"& .MuiPaper-root\": {\n                transition: \"width 0.3s ease, height 0.3s ease\",\n                width: isMaximized ? \"100%\" : \"auto\",\n                height: isMaximized ? \"100%\" : \"auto\",\n                maxWidth: isMaximized ? \"100%\" : \"900px\",\n                maxHeight: isMaximized ? \"100%\" : \"800px\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: [\n                    \"Marcar Coordenadas\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        \"aria-label\": \"close\",\n                        onClick: onClose,\n                        sx: {\n                            position: \"absolute\",\n                            right: 8,\n                            top: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 785,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 783,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        sx: {\n                            backgroundColor: \"#f0f4f8\",\n                            padding: \"20px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 6px rgba(0,0,0,0.1)\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            marginTop: \"15px\",\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    color: \"#5C6BC0\",\n                                    fontSize: \"30px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    color: \"#555\",\n                                    fontSize: \"16px\",\n                                    fontWeight: 400\n                                },\n                                children: \"Marque 4 puntos en el mapa para definir el lote y/o parcela. Ingrese un nombre y el sistema calcular\\xe1 autom\\xe1ticamente el \\xe1rea. Use el bot\\xf3n superior derecho para cambiar la vista.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: mapContainerRef,\n                        style: {\n                            position: \"relative\",\n                            height: \"500px\",\n                            border: \"2px solid black\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 819,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        mt: 2,\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: 2,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                label: \"Nombre del Lote\",\n                                variant: \"outlined\",\n                                value: nombre,\n                                onChange: handleNameChange,\n                                InputProps: {\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    }\n                                },\n                                sx: {\n                                    width: \"300px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                display: \"flex\",\n                                gap: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Coordenadas\",\n                                        value: coordinatesString,\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 3\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Superficie (ha)\",\n                                        value: areaInHectares ? \"\".concat(areaInHectares, \" ha\") : \"\",\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 794,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    onClick: handleLoadValues,\n                    color: \"primary\",\n                    children: \"Cargar Valores\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n        lineNumber: 768,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MapDialog, \"Et7s0ulCz9xzg3s0PVQqtquM0x4=\");\n_c = MapDialog;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapDialog);\nvar _c;\n$RefreshReg$(_c, \"MapDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx\n"));

/***/ })

});