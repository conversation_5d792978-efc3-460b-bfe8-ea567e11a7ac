"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/establecimiento/page",{

/***/ "(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx":
/*!*************************************************!*\
  !*** ./src/app/components/mapbox/MapDialog.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mapbox-gl */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.js\");\n/* harmony import */ var mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mapbox_gl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var mapbox_gl_dist_mapbox_gl_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mapbox-gl/dist/mapbox-gl.css */ \"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.css\");\n/* harmony import */ var _mapbox_mapbox_gl_draw_dist_mapbox_gl_draw_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css */ \"(app-pages-browser)/./node_modules/@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/icons-material/Close */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _turf_turf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @turf/turf */ \"(app-pages-browser)/./node_modules/@turf/area/dist/esm/index.js\");\n/* harmony import */ var _mui_system__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/system */ \"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js\");\n/* harmony import */ var _mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/Map */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Map.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst styles = \"\\n  .mapboxgl-ctrl-custom {\\n    background: #fff;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    height: 29px;\\n    width: 29px;\\n    padding: 0;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .mapboxgl-ctrl-custom:hover {\\n    background-color: #f2f2f2;\\n  }\\n\";\nconst MapDialog = (param)=>{\n    let { openDialog, onClose, onSave, establecimientoId, lotes = [], selectedLoteId = null } = param;\n    _s();\n    const [isMaximized, setIsMaximized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mapStyle, setMapStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mapbox://styles/mapbox/streets-v11\");\n    const mapContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [points, setPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [areaInHectares, setAreaInHectares] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allLotes, setAllLotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isParcelas, setIsParcelas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nombre, setNombre] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Al inicio del componente MapDialog, añade este log\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            // Inspeccionar el formato de las coordenadas\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                    }\n                }[\"MapDialog.useEffect\"]);\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        lotes\n    ]);\n    // Calcula el área del polígono usando turf y actualiza el estado\n    const obtenrDatosSuperficie = ()=>{\n        if (points.length === 4) {\n            const polygonCoords = points.map((p)=>[\n                    p.longitude,\n                    p.latitude\n                ]);\n            // Cerrar el polígono agregando el primer punto al final\n            polygonCoords.push(polygonCoords[0]);\n            const polygonGeoJSON = {\n                type: \"Feature\",\n                properties: {},\n                geometry: {\n                    type: \"Polygon\",\n                    coordinates: [\n                        polygonCoords\n                    ]\n                }\n            };\n            const areaInSquareMeters = _turf_turf__WEBPACK_IMPORTED_MODULE_5__.area(polygonGeoJSON);\n            const areaHectaresCalc = areaInSquareMeters / 10000;\n            setAreaInHectares(areaHectaresCalc.toFixed(2));\n        } else {\n            setAreaInHectares(\"\");\n        }\n    };\n    // Captura el clic en el mapa y añade el punto (máximo 4)\n    const handleMapClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MapDialog.useCallback[handleMapClick]\": (e)=>{\n            setPoints({\n                \"MapDialog.useCallback[handleMapClick]\": (prevPoints)=>{\n                    if (prevPoints.length >= 4) return prevPoints;\n                    return [\n                        ...prevPoints,\n                        {\n                            longitude: e.lngLat.lng,\n                            latitude: e.lngLat.lat\n                        }\n                    ];\n                }\n            }[\"MapDialog.useCallback[handleMapClick]\"]);\n        }\n    }[\"MapDialog.useCallback[handleMapClick]\"], []);\n    // Función para encontrar la ubicación actual\n    const handleFindLocation = ()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const { latitude, longitude } = position.coords;\n                if (mapRef.current) {\n                    mapRef.current.flyTo({\n                        center: [\n                            longitude,\n                            latitude\n                        ],\n                        zoom: 14\n                    });\n                }\n            });\n        }\n    };\n    // Función para eliminar puntos (resetear)\n    const handleDelete = ()=>{\n        setPoints([]);\n    };\n    // Función para cambiar el estilo del mapa\n    const toggleMapStyle = ()=>{\n        const mapStyles = [\n            {\n                value: \"mapbox://styles/mapbox/streets-v11\",\n                label: \"Calles\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/satellite-v9\",\n                label: \"Satélite\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/light-v10\",\n                label: \"Luz\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/dark-v10\",\n                label: \"Oscuro\"\n            },\n            {\n                value: \"mapbox://styles/mapbox/outdoors-v11\",\n                label: \"Afuera\"\n            }\n        ];\n        const currentIndex = mapStyles.findIndex((style)=>style.value === mapStyle);\n        const nextIndex = (currentIndex + 1) % mapStyles.length;\n        setMapStyle(mapStyles[nextIndex].value);\n    };\n    const mapStyles = [\n        {\n            value: \"mapbox://styles/mapbox/streets-v11\",\n            label: \"Calles\",\n            icon: \"🗺️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/satellite-v9\",\n            label: \"Satélite\",\n            icon: \"🛰️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/light-v10\",\n            label: \"Luz\",\n            icon: \"☀️\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/dark-v10\",\n            label: \"Oscuro\",\n            icon: \"🌙\"\n        },\n        {\n            value: \"mapbox://styles/mapbox/outdoors-v11\",\n            label: \"Afuera\",\n            icon: \"🏞️\"\n        }\n    ];\n    // Función para inicializar el mapa (extraída para evitar duplicación)\n    const initializeMap = (center)=>{\n        console.log(\"Inicializando mapa en:\", center);\n        const map = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Map)({\n            container: mapContainerRef.current,\n            style: mapStyle,\n            center: center,\n            zoom: 14,\n            projection: \"mercator\"\n        });\n        mapRef.current = map;\n        // Agrega el listener para capturar clics\n        map.on(\"click\", handleMapClick);\n        map.on(\"load\", ()=>{\n            console.log(\"Mapa cargado en coordenadas:\", center);\n            // Agregar controles básicos\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().NavigationControl)(), \"top-right\");\n            map.addControl(new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().FullscreenControl)(), \"top-left\");\n            // Añadir un marcador en la ubicación actual\n            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                color: \"#FF0000\"\n            }).setLngLat(center).addTo(map);\n            // Agregar botón personalizado para ir a Suardi\n            class SuardiButton {\n                onAdd(map) {\n                    this._map = map;\n                    this._container = document.createElement(\"div\");\n                    this._container.className = \"mapboxgl-ctrl mapboxgl-ctrl-group\";\n                    const button = document.createElement(\"button\");\n                    button.className = \"mapboxgl-ctrl-custom\";\n                    button.type = \"button\";\n                    button.title = \"Ir a Suardi\";\n                    button.innerHTML = '<span style=\"font-size: 18px;\">🏠</span>'; // Emoji de casa\n                    button.onclick = ()=>{\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    };\n                    this._container.appendChild(button);\n                    return this._container;\n                }\n                onRemove() {\n                    if (this._container && this._container.parentNode) {\n                        this._container.parentNode.removeChild(this._container);\n                    }\n                }\n            }\n            // Agregar el botón personalizado al mapa\n            map.addControl(new SuardiButton(), \"top-right\");\n            // Dibujar lotes existentes\n            if (lotes && Array.isArray(lotes) && lotes.length > 0) {\n                console.log(\"Dibujando lotes existentes:\", lotes);\n                lotes.forEach((lote, index)=>{\n                    if (lote.coordinates) {\n                        console.log(\"Dibujando lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), lote.coordinates);\n                        // Parse coordinates if they're a string\n                        let coordinatesArray = lote.coordinates;\n                        if (!Array.isArray(coordinatesArray)) {\n                            try {\n                                // Try to parse if it's a JSON string\n                                coordinatesArray = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                console.log(\"Coordenadas parseadas:\", coordinatesArray);\n                            } catch (e) {\n                                console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                return; // Skip this lote if coordinates can't be parsed\n                            }\n                        }\n                        // Ensure coordinates is an array\n                        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n                            console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                            return;\n                        }\n                        // Create a unique ID for each source and layer\n                        const sourceId = \"lote-source-\".concat(lote.id);\n                        const fillLayerId = \"lote-fill-\".concat(lote.id);\n                        const lineLayerId = \"lote-line-\".concat(lote.id);\n                        try {\n                            // Verify and correct the format of coordinates if necessary\n                            // GeoJSON expects coordinates in [longitude, latitude] format\n                            const correctedCoordinates = coordinatesArray.map((coord)=>{\n                                // If coordinates appear to be in [latitude, longitude] format\n                                if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                                    console.log(\"Invirtiendo coordenadas:\", coord, \"->\", [\n                                        coord[1],\n                                        coord[0]\n                                    ]);\n                                    return [\n                                        coord[1],\n                                        coord[0]\n                                    ]; // Invert to [longitude, latitude]\n                                }\n                                return coord; // Already in [longitude, latitude] format\n                            });\n                            // Verify that there are at least 3 points to form a polygon\n                            if (correctedCoordinates.length < 3) {\n                                console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene suficientes puntos para formar un pol\\xedgono\"));\n                                return;\n                            }\n                            // Create a GeoJSON for the lot\n                            const loteGeoJSON = {\n                                type: \"Feature\",\n                                properties: {\n                                    id: lote.id,\n                                    name: lote.name,\n                                    area: lote.area\n                                },\n                                geometry: {\n                                    type: \"Polygon\",\n                                    coordinates: [\n                                        correctedCoordinates\n                                    ]\n                                }\n                            };\n                            // Add the source to the map\n                            map.addSource(sourceId, {\n                                type: \"geojson\",\n                                data: loteGeoJSON\n                            });\n                            // Obtener el color para este establecimiento\n                            const establecimientoColor = getColorForEstablecimiento(establecimientoId);\n                            // Añadir fill layer con el color del establecimiento\n                            map.addLayer({\n                                id: fillLayerId,\n                                type: \"fill\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"fill-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff9900\",\n                                        establecimientoColor\n                                    ],\n                                    \"fill-opacity\": 0.5\n                                }\n                            });\n                            // Añadir esta propiedad al mapa para mejorar la proyección\n                            map.addSource(\"mapbox-dem\", {\n                                type: \"raster-dem\",\n                                url: \"mapbox://mapbox.mapbox-terrain-dem-v1\",\n                                tileSize: 512,\n                                maxzoom: 14\n                            });\n                            map.setTerrain({\n                                source: \"mapbox-dem\",\n                                exaggeration: 1.0\n                            });\n                            // Añadir line layer con el color del establecimiento\n                            map.addLayer({\n                                id: lineLayerId,\n                                type: \"line\",\n                                source: sourceId,\n                                layout: {},\n                                paint: {\n                                    \"line-color\": [\n                                        \"case\",\n                                        [\n                                            \"==\",\n                                            [\n                                                \"get\",\n                                                \"id\"\n                                            ],\n                                            selectedLoteId\n                                        ],\n                                        \"#ff6600\",\n                                        establecimientoColor\n                                    ],\n                                    \"line-width\": 2\n                                }\n                            });\n                            // Add label with lot name\n                            new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                                element: createLabelElement(lote.name, lote.area),\n                                anchor: \"center\"\n                            }).setLngLat(getCentroid(lote.coordinates)).addTo(map);\n                        } catch (error) {\n                            console.error(\"Error al dibujar el lote \".concat(lote.id, \" (\").concat(lote.name, \"):\"), error);\n                        }\n                    } else {\n                        console.warn(\"El lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas para dibujar\"));\n                    }\n                });\n                // If there are lots, adjust the view to show them all\n                if (lotes.length > 0) {\n                    try {\n                        // Create a bounds that includes all lots\n                        const bounds = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().LngLatBounds)();\n                        let hasValidCoordinates = false;\n                        lotes.forEach((lote)=>{\n                            // Get coordinates and parse if needed\n                            let loteCoordinates = lote.coordinates;\n                            if (!Array.isArray(loteCoordinates)) {\n                                try {\n                                    loteCoordinates = typeof lote.coordinates === \"string\" ? JSON.parse(lote.coordinates) : lote.coordinates;\n                                } catch (e) {\n                                    console.error(\"Error al parsear coordenadas del lote \".concat(lote.id, \":\"), e);\n                                    return; // Skip this lote\n                                }\n                            }\n                            if (Array.isArray(loteCoordinates) && loteCoordinates.length > 0) {\n                                loteCoordinates.forEach((coord)=>{\n                                    // Verify that the coordinates are valid and close to Suardi\n                                    const correctedCoord = Math.abs(coord[0]) <= 90 ? [\n                                        coord[1],\n                                        coord[0]\n                                    ] : coord;\n                                    // Verify that the coordinates are within a reasonable range near Suardi\n                                    const suardiLat = -30.5351;\n                                    const suardiLng = -61.9625;\n                                    const maxDistance = 2; // Maximum degrees of distance (approximately 200km)\n                                    if (Math.abs(correctedCoord[1] - suardiLat) < maxDistance && Math.abs(correctedCoord[0] - suardiLng) < maxDistance) {\n                                        bounds.extend(correctedCoord);\n                                        hasValidCoordinates = true;\n                                    } else {\n                                        console.warn(\"Coordenada ignorada por estar demasiado lejos de Suardi:\", correctedCoord);\n                                    }\n                                });\n                            }\n                        });\n                        // Adjust the map to show all valid lots\n                        if (hasValidCoordinates) {\n                            map.fitBounds(bounds, {\n                                padding: 50,\n                                maxZoom: 15\n                            });\n                        } else {\n                            // If no valid coordinates, center on Suardi\n                            const suardiCoords = [\n                                -61.9625,\n                                -30.5351\n                            ];\n                            map.flyTo({\n                                center: suardiCoords,\n                                zoom: 14,\n                                essential: true\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error al ajustar la vista del mapa:\", error);\n                        // In case of error, center on Suardi\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ];\n                        map.flyTo({\n                            center: suardiCoords,\n                            zoom: 14,\n                            essential: true\n                        });\n                    }\n                } else {\n                    // If no lots, center on Suardi\n                    const suardiCoords = [\n                        -61.9625,\n                        -30.5351\n                    ];\n                    map.flyTo({\n                        center: suardiCoords,\n                        zoom: 14,\n                        essential: true\n                    });\n                }\n            }\n        });\n    };\n    // Función auxiliar para crear un elemento de etiqueta\n    const createLabelElement = (name, area)=>{\n        const el = document.createElement(\"div\");\n        el.className = \"lote-label\";\n        el.style.backgroundColor = \"white\";\n        el.style.border = \"1px solid \".concat(getColorForEstablecimiento(establecimientoId));\n        el.style.borderRadius = \"4px\";\n        el.style.padding = \"4px 8px\";\n        el.style.fontSize = \"12px\";\n        el.style.fontWeight = \"bold\";\n        el.style.whiteSpace = \"nowrap\";\n        el.style.pointerEvents = \"none\";\n        el.innerHTML = \"\".concat(name, \" (\").concat(area.toFixed(2), \" ha)\");\n        return el;\n    };\n    // Función auxiliar para obtener el centroide de un polígono\n    const getCentroid = (coordinates)=>{\n        // Parse coordinates if they're a string\n        let coordinatesArray = coordinates;\n        if (!Array.isArray(coordinatesArray)) {\n            try {\n                coordinatesArray = JSON.parse(coordinates);\n            } catch (e) {\n                console.error(\"Error al parsear coordenadas para centroide:\", e);\n                return [\n                    -61.9625,\n                    -30.5351\n                ]; // Default to Suardi coordinates\n            }\n        }\n        // Ensure coordinates is an array\n        if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {\n            return [\n                -61.9625,\n                -30.5351\n            ]; // Default to Suardi coordinates\n        }\n        // Verificar si las coordenadas están en formato [latitud, longitud]\n        const correctedCoords = coordinatesArray.map((coord)=>{\n            if (Array.isArray(coord) && coord.length >= 2 && Math.abs(coord[0]) <= 90 && Math.abs(coord[1]) <= 180) {\n                return [\n                    coord[1],\n                    coord[0]\n                ]; // Convertir a [longitud, latitud]\n            }\n            return coord;\n        });\n        let sumX = 0;\n        let sumY = 0;\n        correctedCoords.forEach((coord)=>{\n            sumX += coord[0];\n            sumY += coord[1];\n        });\n        return [\n            sumX / correctedCoords.length,\n            sumY / correctedCoords.length\n        ];\n    };\n    // Inicializa el mapa cuando se abre el diálogo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!openDialog) return;\n            // Reinicia datos al abrir el diálogo\n            setPoints([]);\n            setAreaInHectares(\"\");\n            // Depuración de lotes\n            console.log(\"MapDialog montado con lotes:\", lotes);\n            if (lotes && lotes.length > 0) {\n                lotes.forEach({\n                    \"MapDialog.useEffect\": (lote)=>{\n                        console.log(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") coordenadas:\"), lote.coordinates);\n                        // Verificar si las coordenadas son válidas\n                        if (!lote.coordinates || !Array.isArray(lote.coordinates) || lote.coordinates.length === 0) {\n                            console.warn(\"Lote \".concat(lote.id, \" (\").concat(lote.name, \") no tiene coordenadas v\\xe1lidas\"));\n                        }\n                    }\n                }[\"MapDialog.useEffect\"]);\n            } else {\n                console.log(\"No hay lotes para mostrar en el mapa\");\n            }\n            const timer = setTimeout({\n                \"MapDialog.useEffect.timer\": ()=>{\n                    if (mapContainerRef.current && !mapRef.current) {\n                        (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().accessToken) = \"pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A\";\n                        // Inicializar directamente en Suardi, Santa Fe\n                        const suardiCoords = [\n                            -61.9625,\n                            -30.5351\n                        ]; // Suardi, Santa Fe\n                        console.log(\"Inicializando mapa en Suardi, Santa Fe\");\n                        initializeMap(suardiCoords);\n                    }\n                }\n            }[\"MapDialog.useEffect.timer\"], 500);\n            return ({\n                \"MapDialog.useEffect\": ()=>{\n                    if (mapRef.current) {\n                        mapRef.current.remove();\n                        mapRef.current = null;\n                    }\n                    clearTimeout(timer);\n                }\n            })[\"MapDialog.useEffect\"];\n        }\n    }[\"MapDialog.useEffect\"], [\n        openDialog,\n        mapStyle,\n        lotes,\n        handleMapClick,\n        establecimientoId\n    ]);\n    // Actualiza marcadores y polígono cuando se actualicen los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            if (!mapRef.current) return;\n            // Properly type the markers array\n            markersRef.current.forEach({\n                \"MapDialog.useEffect\": (marker)=>marker.remove()\n            }[\"MapDialog.useEffect\"]);\n            markersRef.current = [];\n            // Create a marker for each point\n            points.forEach({\n                \"MapDialog.useEffect\": (p)=>{\n                    const marker = new (mapbox_gl__WEBPACK_IMPORTED_MODULE_2___default().Marker)({\n                        scale: 0.6\n                    }).setLngLat([\n                        p.longitude,\n                        p.latitude\n                    ]).addTo(mapRef.current); // Use non-null assertion since we checked above\n                    markersRef.current.push(marker);\n                }\n            }[\"MapDialog.useEffect\"]);\n            // If there are 4 points, draw the polygon\n            if (points.length === 4) {\n                const polygonCoords = points.map({\n                    \"MapDialog.useEffect.polygonCoords\": (p)=>[\n                            p.longitude,\n                            p.latitude\n                        ]\n                }[\"MapDialog.useEffect.polygonCoords\"]);\n                polygonCoords.push(polygonCoords[0]); // Close the polygon\n                const polygonGeoJSON = {\n                    type: \"Feature\",\n                    properties: {},\n                    geometry: {\n                        type: \"Polygon\",\n                        coordinates: [\n                            polygonCoords\n                        ]\n                    }\n                };\n                if (mapRef.current.getSource(\"polygon\")) {\n                    const source = mapRef.current.getSource(\"polygon\");\n                    source.setData(polygonGeoJSON);\n                } else {\n                    mapRef.current.addSource(\"polygon\", {\n                        type: \"geojson\",\n                        data: polygonGeoJSON\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-fill\",\n                        type: \"fill\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"fill-color\": \"#0080ff\",\n                            \"fill-opacity\": 0.5\n                        }\n                    });\n                    mapRef.current.addLayer({\n                        id: \"polygon-outline\",\n                        type: \"line\",\n                        source: \"polygon\",\n                        layout: {},\n                        paint: {\n                            \"line-color\": \"#0080ff\",\n                            \"line-width\": 2\n                        }\n                    });\n                }\n            } else {\n                // Remove polygon if it exists\n                if (mapRef.current.getLayer(\"polygon-fill\")) {\n                    mapRef.current.removeLayer(\"polygon-fill\");\n                }\n                if (mapRef.current.getLayer(\"polygon-outline\")) {\n                    mapRef.current.removeLayer(\"polygon-outline\");\n                }\n                if (mapRef.current.getSource(\"polygon\")) {\n                    mapRef.current.removeSource(\"polygon\");\n                }\n            }\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Recalcula el área cada vez que se actualizan los puntos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapDialog.useEffect\": ()=>{\n            obtenrDatosSuperficie();\n        }\n    }[\"MapDialog.useEffect\"], [\n        points\n    ]);\n    // Genera el string con todas las coordenadas, separándolas con \" | \"\n    const coordinatesString = points.map((p)=>\"\".concat(p.longitude.toFixed(6), \", \").concat(p.latitude.toFixed(6))).join(\" | \");\n    // Guarda los datos y resetea el estado\n    const handleLoadValues = ()=>{\n        if (points.length >= 4) {\n            obtenrDatosSuperficie();\n            const data = {\n                points: points.map((p)=>({\n                        latitude: p.latitude,\n                        longitude: p.longitude\n                    })),\n                areaInHectares: Number(areaInHectares),\n                name: nombre\n            };\n            onSave(data); // Pass data to parent component without showing alert\n            onClose();\n        } else {\n            // Optional: Add error handling for incomplete polygon\n            alert(\"Por favor, marque 4 puntos en el mapa para definir el lote.\");\n        }\n    };\n    // Añade un manejador de cambios para el TextField\n    const handleNameChange = (e)=>{\n        setNombre(e.target.value);\n    };\n    // Añade esta función para generar colores basados en el ID del establecimiento\n    const getColorForEstablecimiento = (establecimientoId)=>{\n        // Lista de colores distintos para diferentes establecimientos\n        const colors = [\n            \"#3498db\",\n            \"#e74c3c\",\n            \"#2ecc71\",\n            \"#f39c12\",\n            \"#9b59b6\",\n            \"#1abc9c\",\n            \"#d35400\",\n            \"#27ae60\",\n            \"#c0392b\",\n            \"#8e44ad\"\n        ];\n        // Convertir el ID a un número para usarlo como índice\n        let numericId = 0;\n        if (establecimientoId) {\n            // Si es un UUID, usar la suma de los códigos de caracteres\n            if (establecimientoId.includes(\"-\")) {\n                numericId = establecimientoId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n            } else {\n                // Si es numérico, convertirlo\n                numericId = parseInt(establecimientoId, 10) || 0;\n            }\n        }\n        // Usar el módulo para obtener un índice dentro del rango de colores\n        const colorIndex = numericId % colors.length;\n        return colors[colorIndex];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        open: openDialog,\n        onClose: onClose,\n        maxWidth: \"md\",\n        fullWidth: true,\n        sx: {\n            \"& .MuiPaper-root\": {\n                transition: \"width 0.3s ease, height 0.3s ease\",\n                width: isMaximized ? \"100%\" : \"auto\",\n                height: isMaximized ? \"100%\" : \"auto\",\n                maxWidth: isMaximized ? \"100%\" : \"900px\",\n                maxHeight: isMaximized ? \"100%\" : \"800px\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: [\n                    \"Marcar Coordenadas\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        \"aria-label\": \"close\",\n                        onClick: onClose,\n                        sx: {\n                            position: \"absolute\",\n                            right: 8,\n                            top: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        sx: {\n                            backgroundColor: \"#f0f4f8\",\n                            padding: \"20px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 6px rgba(0,0,0,0.1)\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"10px\",\n                            marginTop: \"15px\",\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Map__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    color: \"#5C6BC0\",\n                                    fontSize: \"30px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    color: \"#555\",\n                                    fontSize: \"16px\",\n                                    fontWeight: 400\n                                },\n                                children: \"Marque 4 puntos en el mapa para definir el lote y/o parcela. Ingrese un nombre y el sistema calcular\\xe1 autom\\xe1ticamente el \\xe1rea. Use el bot\\xf3n superior derecho para cambiar la vista.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: mapContainerRef,\n                        style: {\n                            position: \"relative\",\n                            height: \"500px\",\n                            border: \"2px solid black\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        mt: 2,\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: 2,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                label: \"Nombre del Lote\",\n                                variant: \"outlined\",\n                                value: nombre,\n                                onChange: handleNameChange,\n                                InputProps: {\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    }\n                                },\n                                sx: {\n                                    width: \"300px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_system__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                display: \"flex\",\n                                gap: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Coordenadas\",\n                                        value: coordinatesString,\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 3\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        label: \"Superficie (ha)\",\n                                        value: areaInHectares ? \"\".concat(areaInHectares, \" ha\") : \"\",\n                                        InputProps: {\n                                            readOnly: true\n                                        },\n                                        disabled: true,\n                                        sx: {\n                                            flex: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                                lineNumber: 841,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                        lineNumber: 830,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 795,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    onClick: handleLoadValues,\n                    color: \"primary\",\n                    children: \"Cargar Valores\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n                lineNumber: 859,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\mapbox\\\\MapDialog.tsx\",\n        lineNumber: 769,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MapDialog, \"Et7s0ulCz9xzg3s0PVQqtquM0x4=\");\n_c = MapDialog;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapDialog);\nvar _c;\n$RefreshReg$(_c, \"MapDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/mapbox/MapDialog.tsx\n"));

/***/ })

});