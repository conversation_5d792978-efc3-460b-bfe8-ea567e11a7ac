"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON>rid,
  Card,
  Typography,
  Box,
  Button,
  Skeleton,
  Tooltip,
} from "@mui/material";
import Stack from "@mui/material/Stack";
import {
  Event as EventIcon,
  DateRange as DateIcon,
  AccessTime as TimeIcon,
  CalendarMonth,
  Campaign,
  Timer,
} from "@mui/icons-material";
import MetricCard from "../../components/cards/MetricCard";
import { CustomChart } from "@/app/components/charts/CustomCharts";
import WeatherWidget from "../../components/weather/WeatherWidget";
import FarmSummary from "../../components/farm/FarmSummary";
import TaskList from "../../components/tasks/TaskList";
import { styled } from "@mui/material/styles";

const Dashboard = () => {
  // Estados para el formulario de eventos y visualización
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [showEventForm, setShowEventForm] = useState(false);
  const [showEvents, setShowEvents] = useState(false);
  const [events, setEvents] = useState<
    Array<{
      id: string;
      date: string;
      title: string;
      description: string;
      time: string;
    }>
  >([]);

  // Activar el estado de loading
  const [loading, setLoading] = useState(true);

  // Efecto para simular la carga de datos
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await new Promise((resolve) => setTimeout(resolve, 1500));
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Función para manejar la selección de fecha
  const handleDateClick = (date: string) => {
    setSelectedDate(date);
    setShowEventForm(true);
    setShowEvents(false);
  };

  const formatearFecha = (fecha: Date) => {
    const fechaFormateada = fecha.toLocaleDateString("es-ES", {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    });

    // Dividir la cadena en palabras
    const palabras = fechaFormateada.split(" ");

    // Capitalizar la primera letra del día y del mes
    palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana
    palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes

    // Unir las palabras de nuevo
    return palabras.join(" ");
  };

  // Añadir esta función para determinar la estación actual
  const obtenerEstacionActual = () => {
    const fecha = new Date();
    const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11
    const dia = fecha.getDate();
    
    // Verano: 21 de diciembre - 20 de marzo
    if ((mes === 12 && dia >= 21) || mes <= 2 || (mes === 3 && dia <= 20)) {
      return "Verano";
    }
    // Otoño: 21 de marzo - 20 de junio
    else if ((mes === 3 && dia >= 21) || mes <= 5 || (mes === 6 && dia <= 20)) {
      return "Otoño";
    }
    // Invierno: 21 de junio - 20 de septiembre
    else if ((mes === 6 && dia >= 21) || mes <= 8 || (mes === 9 && dia <= 20)) {
      return "Invierno";
    }
    // Primavera: 21 de septiembre - 20 de diciembre
    else {
      return "Primavera";
    }
  };

  // Función para determinar el ciclo agrícola
  const obtenerCicloAgricola = () => {
    const estacion = obtenerEstacionActual();
    return (estacion === "Otoño" || estacion === "Invierno") 
      ? "Otoño-Invierno" 
      : "Primavera-Verano";
  };

  const StyledCard = styled(Card)(({ theme }) => ({
    padding: theme.spacing(2),
    backgroundColor: "#ffffff",
    borderRadius: "8px",
    border: "1px solid #E5E7EB",
    boxShadow: "2px 2px 0px rgba(31, 142, 235, 0.2)",
    marginBottom: theme.spacing(2),
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "translate(-1px, -1px)",
      boxShadow: "3px 3px 0px rgba(31, 142, 235, 0.3)",
    },
  }));

  const StyledRow = styled(Stack)(({ theme }) => ({
    padding: theme.spacing(1, 0),
    transition: "background-color 0.3s ease",
    borderRadius: "8px",
    "&:hover": {
      backgroundColor: "rgba(33, 150, 243, 0.08)",
    },
  }));

  return (
    <Box sx={{ padding: "16px" }}>
      <Box sx={{ mb: 4 }}>
        <Grid
          container
          spacing={2}
          justifyContent="space-between"
          alignItems="flex-start"
        >
          <Grid item xs={12} sm={3}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                mb: 2,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  mb: 1, // Margen entre el título y el subtítulo
                  whiteSpace: "nowrap",
                }}
              >
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  sx={{
                    color: "#2E7D32",
                    fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
                    fontSize: { xs: "1.5rem", sm: "2rem" },
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                  }}
                >
                  Dashboard
                </Typography>
              </Box>
              <Typography
                variant="subtitle1"
                sx={{
                  color: "#666",
                  fontFamily: "Inter",
                  fontSize: { xs: "1rem", sm: "1.2rem" },
                  whiteSpace: "nowrap", // Agregamos esta propiedad para evitar el salto de línea
                }}
              >
                Bienvenido a su sistema de servicios agropecuarios
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={3}>
            {loading ? (
              <Box
                sx={{
                  bgcolor: "#F1F8E9",
                  p: 2,
                  borderRadius: 2,
                  border: "1px solid #C5E1A5",
                  height: "100%", // Asegura que el skeleton tenga la misma altura
                }}
              >
                <Skeleton variant="text" width="60%" height={24} />
                <Skeleton
                  variant="text"
                  width="100%"
                  height={20}
                  sx={{ mt: 1 }}
                />
                <Skeleton
                  variant="text"
                  width="40%"
                  height={16}
                  sx={{ mt: 1 }}
                />
              </Box>
            ) : (
              <StyledCard>
                <Stack spacing={2}>
                  {/* Primera fila: Temporada Actual */}
                  <StyledRow direction="row" alignItems="center" spacing={1}>
                    <Tooltip title="Temporada Actual">
                      <Timer sx={{ color: "#2196F3", fontSize: "1.5rem" }} />
                    </Tooltip>
                    <Box>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontWeight: 600,
                          color: "#000000",
                          fontSize: "1.1rem",
                          fontFamily: "Lexend, sans-serif",
                          letterSpacing: "0.5px",
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        Temporada Actual
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "#666666",
                          fontSize: "0.9rem",
                          fontFamily: "Inter",
                        }}
                      >
                        {obtenerEstacionActual()} ({obtenerCicloAgricola()})
                      </Typography>
                    </Box>
                  </StyledRow>

                  {/* Segunda fila: Fecha Actual */}
                  <StyledRow direction="row" alignItems="center" spacing={1}>
                    <Tooltip title="Fecha Actual">
                      <CalendarMonth
                        sx={{ color: "#FF0000", fontSize: "1.4rem" }}
                      />
                    </Tooltip>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "#666666",
                        fontSize: "1rem",
                        fontFamily: "Inter",
                        fontWeight: 500,
                        textTransform: "none",
                      }}
                    >
                      {formatearFecha(new Date())}
                    </Typography>
                  </StyledRow>

                  {/* Separador visual */}
                  <Stack
                    sx={{
                      borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                      mt: 2,
                      pt: 2,
                    }}
                  />

                  {/* Tercera fila: Campaña */}
                  <StyledRow direction="row" alignItems="center" spacing={1}>
                    <Tooltip title="Campaña Actual">
                      <Campaign sx={{ color: "#786D5F", fontSize: "1.4rem" }} />
                    </Tooltip>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666666",
                        fontFamily: "Lexend, sans-serif",
                        fontWeight: 600,
                        fontSize: "0.9rem",
                        display: "flex",
                        alignItems: "center",
                        gap: 0.5,
                      }}
                    >
                      Campaña {new Date().getFullYear()}/
                      {new Date().getFullYear() + 1}
                    </Typography>
                  </StyledRow>
                </Stack>
              </StyledCard>
            )}
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={3}>
        {/* Sección de Cards */}
        <Grid item xs={12}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <MetricCard
                title="Total de Lotes"
                value={""}
                change="+5% desde ayer"
                icon="carduno.png"
                loading={loading}
                bgColor={""}
                hoverColor={""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <MetricCard
                title="Área Total"
                value={""}
                change="+3% desde la semana pasada"
                icon="carddos.png"
                loading={loading}
                bgColor={""}
                hoverColor={""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <MetricCard
                title="Total de Parcelas"
                value={""}
                change="-2% desde el trimestre pasado"
                icon="cardtres.png"
                loading={loading}
                bgColor={""}
                hoverColor={""}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Sección de Farm Summary y Weather Widget */}
        <Grid item xs={12}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              {loading ? (
                <Box
                  sx={{
                    bgcolor: "#F1F8E9",
                    p: 2,
                    borderRadius: 2,
                    border: "1px solid #C5E1A5",
                    minHeight: "200px", // Altura mínima fija
                    maxHeight: "fit-content", // Altura máxima adaptable
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  {/* Header skeleton */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      pb: 2,
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    <Skeleton variant="text" width="40%" height={32} />
                    <Skeleton variant="text" width="15%" height={24} />
                  </Box>

                  {/* Cards grid skeleton */}
                  <Box sx={{ mt: 2, flex: 1 }}>
                    <Grid container spacing={2}>
                      {[1, 2, 3, 4, 5, 6].map((item) => (
                        <Grid item xs={12} sm={6} md={4} key={item}>
                          <Box
                            sx={{
                              p: 2,
                              bgcolor: "#f8fafc",
                              borderRadius: 2,
                              height: "100px", // Altura fija para cada card
                            }}
                          >
                            <Skeleton variant="text" width="80%" height={24} />
                            <Skeleton
                              variant="text"
                              width="60%"
                              height={20}
                              sx={{ mt: 1 }}
                            />
                            <Skeleton
                              variant="text"
                              width="70%"
                              height={20}
                              sx={{ mt: 1 }}
                            />
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                </Box>
              ) : (
                <FarmSummary />
              )}
            </Grid>
            <Grid item xs={12} md={4}>
              {loading ? (
                <Box
                  sx={{
                    bgcolor: "#F1F8E9",
                    p: 2,
                    borderRadius: 2,
                    border: "1px solid #C5E1A5",
                    height: "100%",
                    maxHeight: "400px", // Altura máxima fija
                  }}
                >
                  <Skeleton variant="text" width="60%" height={24} />
                  <Skeleton variant="rectangular" height={200} sx={{ mt: 2 }} />
                  <Box sx={{ mt: 2 }}>
                    <Skeleton variant="text" width="40%" height={20} />
                    <Skeleton variant="text" width="60%" height={20} />
                    <Skeleton variant="text" width="80%" height={20} />
                  </Box>
                </Box>
              ) : (
                <WeatherWidget />
              )}
            </Grid>
          </Grid>
        </Grid>

        {/* Sección de TaskList */}
        <Grid item xs={12}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              {loading ? (
                <Box
                  sx={{
                    bgcolor: "#F1F8E9",
                    p: 2,
                    borderRadius: 2,
                    border: "1px solid #C5E1A5",
                    height: "100%",
                  }}
                >
                  {/* Header skeleton */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      pb: 2,
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    <Skeleton variant="text" width="40%" height={32} />
                    <Skeleton variant="text" width="15%" height={24} />
                  </Box>

                  {/* Tasks skeleton */}
                  <Box sx={{ mt: 2 }}>
                    {[1, 2, 3, 4, 5].map((item) => (
                      <Box
                        key={item}
                        sx={{
                          mb: 2,
                          p: 2,
                          bgcolor: "#f8fafc",
                          borderRadius: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                          }}
                        >
                          <Box sx={{ display: "flex", gap: 1, width: "70%" }}>
                            <Skeleton
                              variant="circular"
                              width={24}
                              height={24}
                            />
                            <Box sx={{ flex: 1 }}>
                              <Skeleton
                                variant="text"
                                width="80%"
                                height={24}
                              />
                              <Skeleton
                                variant="text"
                                width="60%"
                                height={20}
                              />
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "flex-end",
                              gap: 1,
                            }}
                          >
                            <Skeleton
                              variant="rectangular"
                              width={80}
                              height={24}
                              sx={{ borderRadius: 1 }}
                            />
                            <Skeleton variant="text" width={100} height={20} />
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              ) : (
                <TaskList limit={5} />
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;


