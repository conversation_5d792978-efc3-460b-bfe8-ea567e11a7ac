"use client";

import { useEffect, useRef, useState } from "react";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CloseIcon from "@mui/icons-material/Close";
import { Card, CardContent,CardHeader, Checkbox, DialogContentText, Divider, List, Paper, SelectChangeEvent,} from "@mui/material";
import { InputAdornment } from "@mui/material";
import { Box } from "@mui/material";
import { Button } from "@mui/material";
import { Dialog, DialogContent, DialogTitle } from "@mui/material";
import { FormControl, FormHelperText } from "@mui/material";
import { Grid } from "@mui/material";
import { IconButton } from "@mui/material";
import { InputLabel } from "@mui/material";
import { MenuItem } from "@mui/material";
import { Select } from "@mui/material";
import { TextField } from "@mui/material";
import { Typography } from "@mui/material";
import React from "react";
import MapDialog from "../../components/mapbox/MapDialog";
import type {<PERSON><PERSON>,<PERSON>rcela as ParcelaType,} from "../../../utiles/validarTerrenoEstablecimiento";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import SearchIcon from "@mui/icons-material/Search";
import {Add as AddIcon,Edit as EditIcon,Delete as DeleteIcon,ExpandMore as ExpandMoreIcon,} from "@mui/icons-material";
import Skeleton from "@mui/material/Skeleton";
import VisibilityIcon from "@mui/icons-material/Visibility";
import Autocomplete from "@mui/material/Autocomplete";
import { Home as HomeIcon } from "@mui/icons-material";
import FarmDetailsDialog from "@/app/components/farm/FarmDetailsDialog";
import { Inter } from "next/font/google";
import AddCircleIcon from "@mui/icons-material/AddCircle";

const inter = Inter({ subsets: ["latin"] });

interface Point {
  latitude: number;
  longitude: number;
}

interface MapData {
  points: Point[];
  areaInHectares: number;
}

interface Parcela {
  id: number;
  name: string;
  coordinates: Point[];
  area: number;
}

interface MapDialogData {
  name: string;
  points: Point[]; // Using the Point interface already defined in your file
  areaInHectares: number;
}

interface Establecimiento {
  id: number;
  nombre: string;
  lugar: string;
  persona: {
    id: string;
    razonSocial: string;
  };
  lotes: Lote[];
}

interface Farm {
  id: number;
  nombre: string;
  propietario: {
    id: string;
    razonSocial: string;
  };
  ciudad: string;
  provincia: string;
  nombreLote: string;
  coordenadasLote: string;
  hectareasLote: number;
  nombreParcela: string;
  coordenadasParcela: string;
  hectareasParcela: number;
}

interface Propietario {
  id: number | string;
  razonSocial: string;
  direccion?: string;
  telefono?: string;
  mail?: string;
}

interface FarmOwner {
  id: string; // Debe ser un UUID como string
  razonSocial: string;
}

interface LoteRequest {
  nombre: string;
  coordenadas: string;
  superficie: string;
}

interface EstablecimientoRequest {
  nombre: string;
  persona_id: string; // UUID como string
  lugar: string;
  lotes: LoteRequest[];
}

interface FormData {
  farmName: string;
  farmOwner: {
    id: string;
    razonSocial: string;
  };
  farmTown: string;
  farmProvince: string;
  plotLandName: string;
  plotLandCoordinates: string;
  plotLandArea: string;
  parcelName: string;
  parcelCoordinates: string;
  parcelArea: string;
}

// Estado inicial del formulario
const initialFormData: FormData = {
  farmName: "",
  farmOwner: {
    id: "", // Asegurarse de que sea string vacío
    razonSocial: "",
  },
  farmTown: "",
  farmProvince: "",
  plotLandName: "",
  plotLandCoordinates: "",
  plotLandArea: "",
  parcelName: "",
  parcelCoordinates: "",
  parcelArea: "",
};

interface EstablecimientoRow {
  name?: string;
  owner?: string;
  area?: string;
  place?: string;
  plot?: string;
  parcels?: string;
}

interface SelectedRow {
  id: number;
  nombre?: string;
  lugar?: string;
  propietario?: {
    id: string;
    razonSocial: string;
  };
}

const Establecimientos = () => {
  const [selectedRow, setSelectedRow] = useState(null);
  const [open, setOpen] = useState(false);
  const [rows, setRows] = useState<Establecimiento[]>([]);
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [filteredRows, setFilteredRows] = useState<EstablecimientoRow[]>([]);
  const [temporalParcel, setTemporalParcel] = useState<Parcela | null>(null);
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [estadoColapseRow, setEstadoColapseRow] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showCoordinatesForm, setShowCoordinatesForm] = useState(false);
  const [error, setError] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedEstablecimiento, setSelectedEstablecimiento] = useState<
    number | null
  >(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [establecimientos, setEstablecimientos] = useState<Establecimiento[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [editingEstablecimientoId, setEditingEstablecimientoId] = useState<
    number | null
  >(null);
  const [editingEstablecimientoIds, setEditingEstablecimientoIds] = useState<
    number[]
  >([]);
  const [previewEstablecimiento, setPreviewEstablecimiento] =
    useState<Establecimiento | null>(null);
  const [lotes, setLotes] = useState<Lote[]>([]);
  const [submittingIds, setSubmittingIds] = useState<number[]>([]);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedEstablecimientoDetails, setSelectedEstablecimientoDetails] =
    useState(null);

  const handleEditClick = (establecimientoId: number) => {
    setEditingEstablecimientoIds((prev) =>
      prev.includes(establecimientoId)
        ? prev.filter((id) => id !== establecimientoId)
        : [...prev, establecimientoId]
    );
  };

  const handleViewDetails = (establecimiento: any) => {
    setSelectedEstablecimientoDetails(establecimiento);
    setDetailsDialogOpen(true);
  };

  // Añade este useEffect para simular la carga
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // 2 segundos de simulaciónn
    return () => clearTimeout(timer);
  }, []);

  const handleSelectedFarm = (farmId: number): void => {
    const selectedFarm = rows.find((row) => row.id === farmId);
    if (selectedFarm) {
      // Split lugar into ciudad and provincia
      const [ciudad, provincia] = selectedFarm.lugar.split(" , ");

      // Create a Farm object from Establecimiento data
      const farmData: Farm = {
        id: selectedFarm.id,
        nombre: selectedFarm.nombre,
        propietario: {
          id: selectedFarm.persona.id.toString(),
          razonSocial: selectedFarm.persona.razonSocial,
        },
        ciudad: ciudad,
        provincia: provincia,
        nombreLote: "", // These fields might need to be populated from somewhere else
        coordenadasLote: "",
        hectareasLote: 0,
        nombreParcela: "",
        coordenadasParcela: "",
        hectareasParcela: 0,
      };

      setFormData({
        farmName: farmData.nombre,
        farmOwner: {
          id: farmData.propietario.id.toString(),
          razonSocial: farmData.propietario.razonSocial,
        },
        farmTown: farmData.ciudad,
        farmProvince: farmData.provincia,
        plotLandName: farmData.nombreLote,
        plotLandCoordinates: farmData.coordenadasLote,
        plotLandArea: farmData.hectareasLote.toString(),
        parcelName: farmData.nombreParcela,
        parcelCoordinates: farmData.coordenadasParcela,
        parcelArea: farmData.hectareasParcela.toString(),
      });
    }
  };

  const [formData, setFormData] = useState(initialFormData);

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearForm();
    setOpen(true);
  };

  const clearForm = () => {
    console.log("Limpiando formulario...");
    setFormData(initialFormData);
    setError({});
    setLotes([]);
    setSuperficie("");
    setNombre("");
    setCoordenadas("");
    console.log("Formulario limpiado");
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  // Ensure value prop is always a string
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    setFormData((prevData) => ({
      ...prevData,
      [name]: value || "", // Ensure value is never undefined
    }));

    // Clear error when user starts typing
    if (error[name]) {
      setError((prevError) => ({
        ...prevError,
        [name]: "",
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const provincias = [
    "Buenos Aires",
    "Catamarca",
    "Chaco",
    "Chubut",
    "Córdoba",
    "Corrientes",
    "Entre Ríos",
    "Formosa",
    "Jujuy",
    "La Pampa",
    "La Rioja",
    "Mendoza",
    "Misiones",
    "Neuquén",
    "Río Negro",
    "Salta",
    "San Juan",
    "San Luis",
    "Santa Cruz",
    "Santa Fe",
    "Santiago del Estero",
    "Tierra del Fuego",
    "Tucumán",
  ];

  const fetchEstablecimientos = async () => {
    try {
      const response = await fetch("http://localhost:8080/api/establecimiento");
      if (!response.ok) {
        if (response.status === 404) {
          console.error("API endpoint not found");
          return [];
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log("Establecimientos fetched:", data);
      
      // Asegúrate de que cada lote tenga la propiedad 'name' correctamente asignada
      const procesedData = data.map((establecimiento: any) => ({
        ...establecimiento,
        lotes: Array.isArray(establecimiento.lotes) 
          ? establecimiento.lotes.map((lote: any) => ({
              ...lote,
              name: lote.nombre || lote.name || `Lote sin nombre (ID: ${lote.id})`,
            }))
          : []
      }));
      
      console.log("Establecimientos procesados:", procesedData);
      setEstablecimientos(procesedData);
    } catch (error) {
      console.error("Error fetching establecimientos:", error);
      setEstablecimientos([]);
    }
  };

  useEffect(() => {
    console.log("📌 Datos recibidos en el frontend:", establecimientos);
  }, [establecimientos]);

  // Añade este useEffect para depurar
  useEffect(() => {
    if (establecimientos.length > 0) {
      console.log("Estructura de establecimientos:", establecimientos);
      establecimientos.forEach(est => {
        console.log(`Establecimiento: ${est.nombre}, Lotes:`, est.lotes);
        if (Array.isArray(est.lotes)) {
          est.lotes.forEach(lote => {
            console.log(`Lote ID: ${lote.id}, Propiedades:`, Object.keys(lote));
            console.log(`Nombre del lote:`, lote.name || lote.name);
          });
        }
      });
    }
  }, [establecimientos]);

  /*CREACION DE UN NUEVO ESTABLECIMIENTO*/
  const handleAddEstablecimiento = async (
    establecimientoId: number | string
  ) => {
    try {
      console.log(
        "Iniciando proceso de guardado para establecimiento ID:",
        establecimientoId
      );

      // Obtener el establecimiento seleccionado
      const establecimiento = establecimientos.find(
        (e) => e.id === establecimientoId
      );
      console.log("Establecimiento encontrado:", establecimiento);

      if (!establecimiento) {
        throw new Error("No se encontró el establecimiento seleccionado");
      }

      // Verificar si hay un nombre en el formulario, si no, usar el nombre del establecimiento
      const nombreEstablecimiento =
        formData.farmName?.trim() || establecimiento.nombre;

      if (!nombreEstablecimiento) {
        throw new Error("El nombre del establecimiento es requerido");
      }

      // Solo requerir propietario si es un nuevo establecimiento
      const esActualizacion = typeof establecimientoId === "string";
      if (!esActualizacion && !formData.farmOwner?.id) {
        throw new Error("Por favor seleccione un propietario");
      }

      // Combinar los lotes existentes con los nuevos
      let lotesParaEnviar: Array<{
        nombre: string;
        coordenadas: string;
        superficie: string;
      }> = [];

      // Primero, agregar los lotes existentes (si es una actualización)
      if (esActualizacion && Array.isArray(establecimiento.lotes)) {
        // Solo incluir lotes que tienen datos válidos
        lotesParaEnviar = establecimiento.lotes
          .filter(lote => lote.name && (lote.coordinates || lote.coordenadas))
          .map((lote) => ({
            nombre: lote.name,
            coordenadas:
              typeof lote.coordinates === "string"
                ? lote.coordinates
                : JSON.stringify(lote.coordinates),
            superficie: lote.area?.toString() || "0",
          }));
      }

      // Luego, agregar los nuevos lotes del formulario si hay alguno
      if (lotes && lotes.length > 0) {
        const nuevosLotes = lotes
          .filter(
            (lote) => 
              // Filtrar solo los lotes que tienen datos válidos y no existen ya
              lote.name && (lote.coordinates || lote.coordenadas) &&
              !lotesParaEnviar.some((l) => l.nombre === lote.name)
          )
          .map((lote) => ({
            nombre: lote.name,
            coordenadas:
              typeof lote.coordinates === "string"
                ? lote.coordinates
                : JSON.stringify(lote.coordinates),
            superficie: lote.area?.toString() || "0",
          }));

        lotesParaEnviar = [...lotesParaEnviar, ...nuevosLotes];
      }

      // Si hay un lote seleccionado, asegurarse de que esté incluido
      if (selectedLoteId) {
        const loteSeleccionado = lotes.find(lote => lote.id === selectedLoteId);
        if (loteSeleccionado && !lotesParaEnviar.some(l => l.nombre === loteSeleccionado.name)) {
          lotesParaEnviar.push({
            nombre: loteSeleccionado.name,
            coordenadas: typeof loteSeleccionado.coordinates === "string"
              ? loteSeleccionado.coordinates
              : JSON.stringify(loteSeleccionado.coordinates),
            superficie: loteSeleccionado.area?.toString() || "0",
          });
        }
      }

      // Usar el lugar del establecimiento si el formulario no tiene datos
      const lugarEstablecimiento = 
        (formData.farmTown?.trim() || formData.farmProvince?.trim()) 
          ? `${formData.farmTown?.trim() || ''}, ${formData.farmProvince?.trim() || ''}`
          : establecimiento.lugar;

      // Preparar el objeto para enviar
      const nuevoEstablecimiento: any = {
        nombre: nombreEstablecimiento,
        lugar: lugarEstablecimiento,
        lotes: lotesParaEnviar
      };
      
      // Si es actualización, incluir el ID
      if (esActualizacion) {
        nuevoEstablecimiento.id = establecimientoId;
        nuevoEstablecimiento.persona_id = establecimiento.persona?.id;
      } else {
        nuevoEstablecimiento.persona_id = formData.farmOwner?.id;
      }
      
      console.log("Datos a enviar:", nuevoEstablecimiento);
      
      const url = "http://localhost:8080/api/establecimiento";
      const method = esActualizacion ? "PUT" : "POST";
      
      console.log(`Realizando ${method} a ${url}`);

      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(nuevoEstablecimiento),
      });

      console.log("Respuesta del servidor:", response);
      console.log("Status:", response.status);
      
      const responseText = await response.text();
      console.log("Respuesta como texto:", responseText);
      
      let responseData;
      try {
        responseData = responseText ? JSON.parse(responseText) : {};
        console.log("Datos de respuesta:", responseData);
      } catch (e) {
        console.error("Error al parsear la respuesta:", e);
      }

      if (!response.ok) {
        throw new Error(
          responseData?.mensaje ||
          responseData?.error ||
          `Error ${response.status}: ${response.statusText}`
        );
      }

      await fetchEstablecimientos();
      clearForm();
      setPreviewEstablecimiento(null);
      setOpen(false);
      alert("Establecimiento guardado exitosamente");
    } catch (err) {
      console.error("Error completo en el proceso de guardado:", err);
      alert("Error al guardar: " + (err instanceof Error ? err.message : "Error desconocido"));
    } finally {
      setSubmittingIds((prev) => prev.filter((id) => id !== establecimientoId));
    }
  };

  useEffect(() => {
    // Construimos la previsualización con un id numérico (usando Date.now())
    const establecimientoPreview: Establecimiento = {
      id: Date.now(), // id numérico
      nombre: formData.farmName,
      lugar: `${formData.farmTown} , ${formData.farmProvince}`,
      persona: {
        id: formData.farmOwner?.id || "",
        razonSocial: formData.farmOwner?.razonSocial || "",
      },
      lotes: lotes.map((lote) => ({
        ...lote,
        parcels: lote.parcels || [],
      })),
    };

    setPreviewEstablecimiento(establecimientoPreview);
  }, [formData, lotes]);

  /*PREVISUALIZAR DATOS DEL ESTABLECIMIENTO EN LA CARDS*/
  const handlePreviewEstablecimiento = () => {
    const establecimientoPreview = {
      id: Date.now(), // Usar timestamp como ID único
      nombre: formData.farmName,
      lugar: `${formData.farmTown} ,${formData.farmProvince}`,
      persona: {
        id: formData.farmOwner.id,
        razonSocial: formData.farmOwner.razonSocial,
      },
      lotes: lotes.map((lote) => ({
        ...lote,
        parcels: lote.parcels || [],
      })),
    };

    // Agregar la previsualización como una nueva card
    setEstablecimientos((prevEstablecimientos) => [
      ...prevEstablecimientos, // Mantener todos los establecimientos existentes
      establecimientoPreview, // Agregar el nuevo preview como una card separada
    ]);
  };

  /*BUSCAR ESTABLECIMIENTO*/
  const handleSearchEstablecimiento = async (value: string) => {
    const searchValue = value.toLowerCase();
    const filteredData = (rows as EstablecimientoRow[]).filter((row) => {
      return (
        (row.name?.toLowerCase() || "").includes(searchValue) ||
        (row.owner?.toLowerCase() || "").includes(searchValue) ||
        (row.area?.toLowerCase() || "").includes(searchValue) ||
        (row.place?.toLowerCase() || "").includes(searchValue) ||
        (row.plot?.toLowerCase() || "").includes(searchValue) ||
        (row.parcels?.toLowerCase() || "").includes(searchValue)
      );
    });
    setFilteredRows(filteredData);
  };

  /*MODIFICAR ESTABLECIMIENTO PARA GAURDAR*/
  const handleUpdateEstablecimiento = async (id: number) => {
    if (!selectedRow) return;

    const updatedFarm = {
      ...formData,
      id: (selectedRow as SelectedRow).id,
      lugar: `${formData.farmTown} , ${formData.farmProvince}`, // Fixed template string syntax
    };

    try {
      const res = await fetch("http://localhost:8080/api/establecimiento", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updatedFarm),
      });

      if (!res.ok) throw new Error("Error al actualizar el establecimiento");

      clearForm();
      setOpen(false);
    } catch (error) {
      console.error("Error en actualización:", error);
    }
  };

  /* BUSCAR AGRICULTOR*/
  const handleSearchFarmerButtonClick = () => {
    // Guardar el estado actual del formulario
    localStorage.setItem("formData", JSON.stringify(formData));
    // Guardar el estado del diálogo
    localStorage.setItem("dialogOpen", "true");
    // Navegar a la página de agricultor en la misma ventana
    window.location.href = "/agricultor";
  };

  useEffect(() => {
    // Get items from localStorage with null checks
    const selectedAgricultorStr = localStorage.getItem("selectedAgricultor");
    const dialogStatus = localStorage.getItem("dialogOpen") === "true";
    const savedFormDataStr = localStorage.getItem("formData");

    // Parse JSON only if the strings exist
    const selectedAgricultor = selectedAgricultorStr
      ? JSON.parse(selectedAgricultorStr)
      : null;
    const savedFormData = savedFormDataStr
      ? JSON.parse(savedFormDataStr)
      : null;

    if (savedFormData) {
      setFormData(savedFormData);
    }

    if (selectedAgricultor) {
      setFormData((prevFormData) => ({
        ...prevFormData,
        farmOwner: selectedAgricultor,
      }));
    }
    if (dialogStatus) {
      setOpen(true);
      localStorage.setItem("dialogOpen", "false");
    }
  }, []);

  useEffect(() => {
    localStorage.removeItem("formData");
  }, []);

  /*OBTENER AGRICULTORES */
  const obtenerClientes = async () => {
    try {
      // Hace una petición GET al endpoint de agricultores
      const response = await fetch("http://localhost:8080/api/agricultor");
      // Verifica si la respuesta es exitosa (estado 200-299)
      if (!response.ok) {
        throw new Error("No se pudo obtener la lista de agricultores");
      }
      // Convierte la respuesta a formato JSON
      const data = await response.json(); // Convertir la respuesta a JSON
      // Verifica que la respuesta sea un array
      if (Array.isArray(data)) {
        // Actualiza el estado 'propietarios' con los datos de los agricultores
        setPropietarios(data);
      } else {
        console.error("Los datos obtenidos no son un array:", data);
        // Manejar el caso en el que los datos no son un array (quizás mostrar un mensaje de error)
      }
    } catch (error) {
      console.error("Error al obtener agricultores:", error);
    }
  };

  useEffect(() => {
    obtenerClientes(); // Llamar a la función para obtener clientes cuando el componente se monte
  }, []);

  const selectProvinciaRef = useRef<HTMLInputElement>(null);

  const handleLocalidadKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter") {
      if (selectProvinciaRef.current) {
        selectProvinciaRef.current.focus();
      }
    }
  };

  const handleProvinciaChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    handleInputChange(event);
    setTimeout(() => {}, 0);
  };

  //CARGAR AGRICULTORES PARA MOSTRAR EN AUTOCOMPLETE
  const [propietarios, setPropietarios] = useState<any[]>([]);
  const [nombre, setNombre] = useState("");
  const [coordenadas, setCoordenadas] = useState("");
  const [superficie, setSuperficie] = useState("");
  const [superficieEstablecimiento, setsuperficieEstablecimiento] =
    useState("");
  const [selectedLoteId, setSelectedLoteId] = useState<number | null>(null);
  const [isParcelas, setIsParcelas] = useState(false);
  const [expandedLoteId, setExpandedLoteId] = useState(null);
  const [loteActual, setLoteActual] = useState<Lote | null>(null);
  const [establecimientoSeleccionado, setEstablecimientoSeleccionado] =
    useState<number | null>(null);

  useEffect(() => {
    if (coordenadas.length > 0) {
      console.log("Actualizando formData con coordenadas:", coordenadas);
      setFormData((prev) => ({
        ...prev,
        [isParcelas ? "parcelCoordinates" : "plotLandCoordinates"]:
          JSON.stringify(coordenadas),
      }));
    }
  }, [coordenadas, isParcelas]);

  const handleCheckboxChange = (loteId: number) => {
    console.log(loteId);
    // Si el lote seleccionado es el mismo que ya estaba seleccionado, lo deseleccion
    if (loteId === selectedLoteId) {
      setSelectedLoteId(null);
    } else {
      // Si es un lote diferente, lo selecciona
      setSelectedLoteId(loteId);
      // Busca el lote seleccionado en el array de lotes
      const loteSeleccionado = lotes.find((lote) => lote.id === loteId);

      if (loteSeleccionado) {
        // Limpia el nombre actual
        setNombre("");
        // Indica que ahora se trabajará con parcelas
        setIsParcelas(true);
      }
    }
  };

  const [openDialog, setOpenDialog] = useState(false); // Estado para controlar el Dialog

  const handleOpenDialog = (idEstablecimiento: number) => {
    setEstablecimientoSeleccionado(idEstablecimiento);
    const establecimiento = establecimientos.find((e) => e.id === idEstablecimiento);
    console.log("Opening dialog with establecimiento:", establecimiento);
    
    if (establecimiento && establecimiento.lotes) {
      // Asegurarse de que lotes sea un array
      const lotesArray = Array.isArray(establecimiento.lotes) 
        ? establecimiento.lotes 
        : [];
      
      // Convertir los lotes al formato esperado por MapDialog
      const formattedLotes = lotesArray.map(lote => ({
        id: lote.id,
        name: lote.name || (lote as any).nombre,
        coordinates: lote.coordinates || (lote as any).coordenadas || [],
        area: lote.area || (lote as any).superficie || 0,
        parcels: lote.parcels || (lote as any).parcelas || []
      }));
      
      console.log("Lotes formateados para MapDialog:", formattedLotes);
      setLotes(formattedLotes.map(lote => ({
        ...lote,
        coordenadas: lote.coordinates // Add the required coordenadas property
      })));
    } else {
      setLotes([]);
    }
    
    const loteEncontrado = establecimiento && Array.isArray(establecimiento.lotes)
      ? establecimiento.lotes.find((l) => l.id === selectedLoteId)
      : null;
    setLoteActual(loteEncontrado || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setShowCoordinatesForm(true);
    setOpenDialog(false);
  };

  /*MAPBOX */
  const [lotPoints, setLotPoints] = useState<Point[]>([]);
  const [parcelPoints, setParcelPoints] = useState<Point[]>([]);

  const handleSave = (data: MapDialogData) => {
    console.log("Guardando nuevo lote con datos:", data);
    
    const nuevoLote: Lote = {
      id: new Date().getTime(), // Asegura un ID único
      name: data.name,
      coordinates: data.points.map((point) => [
        point.longitude,
        point.latitude,
      ]),
      coordenadas: data.points.map((point) => [
        point.longitude,
        point.latitude,
      ]),
      area: data.areaInHectares,
      parcels: [],
    };
    
    console.log("Nuevo lote creado:", nuevoLote);

    // Actualiza el estado de lotes para incluir el nuevo lote
    setLotes(prevLotes => [...prevLotes, nuevoLote]);
    
    // Actualiza el estado de establecimientos
    setEstablecimientos((establecimientosAnteriores) =>
      establecimientosAnteriores.map((establecimiento) =>
        establecimiento.id === establecimientoSeleccionado
          ? {
              ...establecimiento,
              lotes: loteActual
                ? establecimiento.lotes.map((lote) =>
                    lote.id === loteActual.id
                      ? {
                          ...lote,
                          coordenadas: data.points.map((point) => [
                            point.longitude,
                            point.latitude,
                          ]),
                          coordinates: data.points.map((point) => [
                            point.longitude,
                            point.latitude,
                          ]),
                          area: data.areaInHectares,
                          name: data.name,
                        }
                      : lote
                  )
                : Array.isArray(establecimiento.lotes) 
                  ? [...establecimiento.lotes, nuevoLote]
                  : [nuevoLote],
            }
          : establecimiento
      )
    );
    
    console.log("Estado de establecimientos actualizado");
  };

  //Muestra los datos de la card en pantalla

  useEffect(() => {
    // Llamar a fetchEstablecimientos cuando el componente se monte
    fetchEstablecimientos();
  }, []);

  const handleSearchClick = () => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  useEffect(() => {
    // Cargar múltiples fuentes de Google
    const link = document.createElement("link");
    link.href = "https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&family=Montserrat:wght@600;700&family=Open+Sans:wght@400;600&family=Lato:wght@400;700&display=swap";
    link.rel = "stylesheet";
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  useEffect(() => {
    console.log("Estado del formulario actualizado:", formData);
  }, [formData]);

  useEffect(() => {
    console.log("Estado de lotes actualizado:", lotes);
  }, [lotes]);

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif",
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{ fontWeight: "bold", fontFamily: "Lexend, sans-serif" }}
          >
            Establecimientos
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: "Inter, sans-serif",
            }}
          >
            Gestión de establecimientos, lotes y parcelas para su cliente.
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32",
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Establecimiento
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleSearchEstablecimiento(e.target.value)
          }
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <IconButton onClick={handleSearchClick}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
      </Paper>

      <Dialog open={open} onClose={handleClickClose} maxWidth="md" fullWidth>
        <Box sx={{ p: 3 }}>
          {" "}
          {/* Add padding container */}
          <Box sx={{ mb: 3 }}>
            {" "}
            {/* Add margin bottom */}
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Registrar nuevo Establecimiento Agricola
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información del nuevo establecimiento a registrar.
            </DialogContentText>
          </Box>
          <IconButton
            aria-label="close"
            onClick={(event: React.MouseEvent<HTMLElement>) =>
              handleClickClose(event, "closeButtonClick")
            }
            sx={{
              position: "absolute",
              right: 16,
              top: 16,
            }}
          >
            <CloseIcon />
          </IconButton>
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              {/* IDENTIFICACION */}
              <Grid
                item
                xs={12}
                sx={{ marginTop: "25px", paddingBottom: "25px" }}
              >
                <Grid container spacing={2} alignItems="center">
                  {/* Nombre de la granja */}
                  <Grid item xs={4}>
                    <Typography variant="body2" sx={labelStyles}>
                      Nombre
                    </Typography>
                    <TextField
                      placeholder="Ej: Granja Juan"
                      variant="outlined"
                      name="farmName"
                      type="text"
                      fullWidth
                      required
                      value={formData.farmName || ""}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const syntheticEvent = {
                          target: {
                            name: e.target.name,
                            value: e.target.value,
                          },
                        } as React.ChangeEvent<
                          HTMLInputElement | HTMLSelectElement
                        >;
                        handleInputChange(syntheticEvent);
                      }}
                      error={Boolean(error.farmName)}
                      helperText={error.farmName}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            {formData.farmName &&
                              (error.farmName ? (
                                <ErrorIcon color="error" />
                              ) : (
                                <CheckCircleIcon color="success" />
                              ))}
                          </InputAdornment>
                        ),
                        style: { fontFamily: "Inter, sans-serif" },
                      }}
                      sx={{
                        "& .MuiInputBase-input": {
                          fontFamily: "Inter, sans-serif",
                        },
                        "& .MuiFormHelperText-root": {
                          fontFamily: "Inter, sans-serif",
                        },
                      }}
                    />
                  </Grid>

                  {/* Dueño y botón de búsqueda */}
                  <Grid item xs={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={7}>
                        <Typography variant="body2" sx={labelStyles}>
                          Propietario
                        </Typography>
                        <Autocomplete
                          value={formData.farmOwner}
                          readOnly={true}
                          disableClearable
                          options={propietarios}
                          getOptionLabel={(option) => option?.razonSocial || ""}
                          onClick={handleSearchFarmerButtonClick}
                          sx={{
                            "& .MuiAutocomplete-inputRoot": {
                              marginTop: "0",
                              marginBottom: "0",
                            },
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              placeholder="Ej: Juan Perez"
                              variant="outlined"
                              fullWidth
                              required
                              error={Boolean(error.farmOwner)}
                              helperText={error.farmOwner}
                              InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {formData.farmOwner?.id &&
                                      !error.farmOwner && (
                                        <CheckCircleIcon color="success" />
                                      )}
                                    {error.farmOwner && (
                                      <ErrorIcon color="error" />
                                    )}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={5}>
                        <Button
                          variant="contained"
                          onClick={handleSearchFarmerButtonClick}
                          startIcon={<SearchIcon />}
                          sx={{ mt: 4.5 }}
                        >
                          Buscar
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sx={{ margin: "8px 0", padding: "8px" }}>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Typography variant="body2" sx={labelStyles}>
                      Localidad
                    </Typography>
                    <TextField
                      placeholder="Ej: Mercedes"
                      variant="outlined"
                      id="localidad"
                      name="farmTown"
                      type="text"
                      error={Boolean(error.farmTown)}
                      helperText={error.farmTown}
                      fullWidth
                      required
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        // Create a synthetic event that matches the expected type
                        const syntheticEvent = {
                          target: {
                            name: e.target.name,
                            value: e.target.value,
                          },
                        } as React.ChangeEvent<
                          HTMLInputElement | HTMLSelectElement
                        >;

                        handleInputChange(syntheticEvent);
                      }}
                      onKeyDown={handleLocalidadKeyDown}
                      value={formData.farmTown}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            {formData.farmTown &&
                              (error.farmTown ? (
                                <ErrorIcon color="error" />
                              ) : (
                                <CheckCircleIcon color="success" />
                              ))}
                          </InputAdornment>
                        ),
                        style: { fontFamily: "Inter, sans-serif" },
                      }}
                      sx={{
                        "& .MuiInputBase-input": {
                          fontFamily: "Inter, sans-serif",
                        },
                        "& .MuiFormHelperText-root": {
                          fontFamily: "Inter, sans-serif",
                        },
                      }}
                      style={{ marginBottom: "16px" }}
                    />
                  </Grid>
                  <Grid item xs={4} sx={{ pl: 2 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Provincia
                    </Typography>
                    <FormControl
                      fullWidth
                      error={Boolean(error.farmProvince)}
                      sx={{ mb: 2 }}
                    >
                      <InputLabel id="demo-simple-select-label">
                        Seleccione una provincia
                      </InputLabel>
                      <Select
                        label="Provincias"
                        id="provincias"
                        name="farmProvince"
                        labelId="demo-simple-select-label"
                        fullWidth
                        value={formData.farmProvince}
                        onChange={(event: SelectChangeEvent<string>) => {
                          // Create a synthetic event that matches the expected type
                          const syntheticEvent = {
                            target: {
                              name: "farmProvince",
                              value: event.target.value,
                            },
                          } as React.ChangeEvent<
                            HTMLInputElement | HTMLSelectElement
                          >;

                          handleProvinciaChange(syntheticEvent);
                        }}
                        inputRef={selectProvinciaRef}
                        endAdornment={
                          formData.farmProvince && !error.farmProvince ? (
                            <InputAdornment position="end">
                              <CheckCircleIcon color="success" />
                            </InputAdornment>
                          ) : null
                        }
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 200, // Altura máxima de 200px
                            },
                          },
                        }}
                        sx={{
                          "& .MuiSelect-select": {
                            fontFamily: "Inter, sans-serif",
                          },
                          "& .MuiFormHelperText-root": {
                            fontFamily: "Inter, sans-serif",
                          },
                        }}
                      >
                        {provincias.map((provincia) => (
                          <MenuItem key={provincia} value={provincia}>
                            {provincia}
                          </MenuItem>
                        ))}
                      </Select>

                      {!formData.farmProvince && error.farmProvince && (
                        <FormHelperText>{error.farmProvince}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={4}>
                    <Button
                      variant="contained"
                      onClick={handlePreviewEstablecimiento}
                      sx={{
                        backgroundColor: "#1976d2",
                        "&:hover": { backgroundColor: "#1565c0" },
                        mt: 4.5,
                      }}
                      startIcon={<VisibilityIcon />}
                    >
                      Previsualizar
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Dialog>

      <Grid container spacing={3}>
        {isLoading
          ? // Skeleton loading
            Array.from(new Array(3)).map((_, index) => (
              <Grid item xs={12} md={6} lg={4} key={`skeleton-${index}`}>
                <Card variant="outlined" sx={{ height: "100%" }}>
                  <CardHeader
                    avatar={
                      <Skeleton
                        animation="wave"
                        variant="circular"
                        width={40}
                        height={40}
                      />
                    }
                    title={
                      <Skeleton animation="wave" height={30} width="80%" />
                    }
                    subheader={
                      <Skeleton animation="wave" height={20} width="60%" />
                    }
                  />
                  <Divider />
                  <CardContent>
                    <Skeleton animation="wave" height={20} width="70%" />
                    <Skeleton animation="wave" height={20} width="50%" />
                    <Skeleton animation="wave" height={60} />
                    <Skeleton animation="wave" height={40} width="90%" />
                    <Skeleton animation="wave" height={40} width="90%" />
                  </CardContent>
                </Card>
              </Grid>
            ))
          : establecimientos.map((establecimiento) => (
              <Grid item xs={12} md={8} lg={6} key={establecimiento.id}>
                {" "}
                {/* Aumentado el ancho */}
                <Card
                  variant="outlined"
                  sx={{
                    height: "100%",
                    border: typeof establecimiento.id === "number" && 
                           establecimiento.id > Date.now() - 60000
                      ? "2px solid #4caf50" // Nuevo borde (verde sólido)
                      : "1px solid rgba(0, 0, 0, 0.12)",
                    backgroundColor: typeof establecimiento.id === "number" && 
                                    establecimiento.id > Date.now() - 60000
                      ? "rgba(25, 118, 210, 0.04)"
                      : "inherit",
                  }}
                >
                  <CardHeader
                    title={
                      <Box>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                            marginBottom: "4px",
                          }}
                        >
                          <HomeIcon sx={{ color: "#2563eb" }} /> {/* Azul */}
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: "bold",
                              fontFamily: "Lexend, sans-serif",
                              fontSize: "1.25rem",
                              color: "text.primary",
                              display: "inline",
                            }}
                          >
                            {establecimiento.nombre}
                          </Typography>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: "normal",
                              fontFamily: "Lexend, sans-serif",
                              fontSize: "1.25rem",
                              color: "text.primary",
                              display: "inline",
                            }}
                          >
                            de {establecimiento.persona?.razonSocial}
                          </Typography>
                        </Box>

                        
                        {editingEstablecimientoIds.includes(
                          establecimiento.id
                        ) && (
                          <IconButton
                            color="primary"
                            onClick={(event: React.MouseEvent) =>
                              handleOpenDialog(establecimiento.id)
                            }
                            sx={{
                              marginLeft: "auto",
                              color: "#059669",
                              padding: "8px", // Añadir padding consistente
                              alignSelf: "center", // Alinear verticalmente
                              height: "40px", // Altura fija igual a otros iconos
                              width: "40px", // Ancho fijo igual a otros iconos
                            }}
                          >
                            <AddIcon />
                          </IconButton>
                        )}
                      </Box>
                    }
                    action={
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <Button
                          type="button" // Explícitamente definido como button
                          onClick={() => handleViewDetails(establecimiento)}
                          startIcon={<VisibilityIcon />}
                          sx={{
                            color: "#666666",
                            border: "1px solid #e0e0e0",
                            textTransform: "none",
                            "&:hover": {
                              backgroundColor: "rgba(0, 0, 0, 0.04)",
                            },
                          }}
                        >
                          Detalles
                        </Button>
                        <Button
                          type="button" // Explícitamente definido como button
                          onClick={(event: React.MouseEvent) =>
                            handleOpenDialog(establecimiento.id)
                          }
                          startIcon={<AddIcon />}
                          sx={{
                            color: "#666666",
                            border: "1px solid #e0e0e0",
                            textTransform: "none",
                            "&:hover": {
                              backgroundColor: "rgba(0, 0, 0, 0.04)",
                            },
                          }}
                        >
                          Añadir Lote
                        </Button>
                      </Box>
                    }
                  />

                  <CardContent>
                    <List>
                      {Array.isArray(establecimiento.lotes) ? 
                        (
                          <>
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: "bold",
                                fontSize: "1rem",
                                fontFamily: "Lexend, sans-serif",
                                marginBottom: "12px", // Aumentar el espacio después del título
                              }}
                            >
                              Lotes:
                            </Typography>
                            {establecimiento.lotes.map((lote, index) => (
                              <Box
                                key={lote.id}
                                sx={{ boxShadow: 0, borderRadius: "8px" }}
                              >
                                <Box
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    width: "100%",
                                    alignItems: "center",
                                    mb: 2,
                                  }}
                                >
                                  <Typography
                                    component="span"
                                    sx={{
                                      fontSize: "1rem",
                                      fontWeight: "bold",
                                      color: "text.primary",
                                      fontFamily: "Lexend, sans-serif",
                                    }}
                                  >
                                    {/* Intenta acceder a todas las posibles propiedades de nombre */}
                                    {lote.name || lote.name || `Lote ${index + 1}`}
                                  </Typography>
                                  <Checkbox
                                    checked={selectedLoteId === lote.id}
                                    onChange={() => handleCheckboxChange(lote.id)}
                                    color="primary"
                                  />
                                </Box>
                                <Divider sx={{ my: 1 }} />
                              </Box>
                            ))}
                          </>
                        ) : 
                        <Typography variant="body2" color="text.secondary">
                          No hay lotes registrados en este establecimiento.
                        </Typography>
                      }

                      {/* Sección de parcelas */}
                      {selectedLoteId && (
                        <>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              fontWeight: "bold",
                              fontSize: "1rem",
                              fontFamily: "Lexend, sans-serif",
                            }}
                          >
                            Parcelas:
                          </Typography>
                          {Array.isArray(lotes.find(l => l.id === selectedLoteId)?.parcels) &&
                          (lotes.find(l => l.id === selectedLoteId)?.parcels?.length ?? 0) > 0 ? (
                            lotes.find(l => l.id === selectedLoteId)?.parcels?.map(
                              (parcela: import("../../../utiles/validarTerrenoEstablecimiento").Parcela) => (
                                <Typography
                                  key={parcela.id}
                                  variant="body2"
                                  sx={{ mb: 1 }}
                                >
                                  <strong>{parcela.name}</strong>: {parcela.area} ha
                                </Typography>
                              )
                            )
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              No hay parcelas registradas en este lote.
                            </Typography>
                          )}
                        </>
                      )}
                    </List>

                    <MapDialog
                      openDialog={openDialog}
                      onClose={handleCloseDialog}
                      onSave={handleSave}
                      establecimientoId={establecimientoSeleccionado?.toString() || ""}
                      lotes={lotes}
                    />

                    <Grid
                      item
                      xs={12}
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        padding: "8px",
                      }}
                    >
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<AddCircleIcon />}
                        onClick={() =>
                          estadoModal === "add"
                            ? handleAddEstablecimiento(establecimiento.id)
                            : handleUpdateEstablecimiento(establecimiento.id)
                        }
                        sx={{
                          bgcolor: "#2E7D32",
                          color: "#ffffff",
                          "&:hover": { bgcolor: "#1B5E20" },
                          textTransform: "none",
                          "& .MuiSvgIcon-root": {
                            color: "#ffffff",
                          },
                        }}
                      >
                        {estadoModal === "add" ? "Registrar" : "Guardar"}
                      </Button>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}
      </Grid>
      {selectedEstablecimientoDetails && (
        <FarmDetailsDialog
          open={detailsDialogOpen}
          onClose={() => setDetailsDialogOpen(false)}
          establecimiento={selectedEstablecimientoDetails}
        />
      )}
    </>
  );
};
export default Establecimientos;
