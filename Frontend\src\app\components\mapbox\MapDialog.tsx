import React, { useEffect, useRef, useState, useCallback } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import * as turf from "@turf/turf";
import { Box } from "@mui/system";
import MapIcon from "@mui/icons-material/Map";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import LocationOnIcon from "@mui/icons-material/LocationOn";

interface Point {
  latitude: number;
  longitude: number;
}

interface MapDialogData {
  name: string;
  points: Point[];
  areaInHectares: number;
}

interface Lote {
  id: number;
  name: string;
  coordinates: number[][];
  area: number;
  parcels: any[];
}

interface MapDialogProps {
  openDialog: boolean;
  onClose: () => void;
  onSave: (data: MapDialogData) => void;
  establecimientoId: string;
  lotes?: Lote[];
  selectedLoteId?: number | null;
}

const styles = `
  .mapboxgl-ctrl-custom {
    background: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 29px;
    width: 29px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mapboxgl-ctrl-custom:hover {
    background-color: #f2f2f2;
  }
`;

const MapDialog: React.FC<MapDialogProps> = ({
  openDialog,
  onClose,
  onSave,
  establecimientoId,
  lotes = [],
  selectedLoteId = null,
}) => {
  const [isMaximized, setIsMaximized] = useState(false);
  const [mapStyle, setMapStyle] = useState(
    "mapbox://styles/mapbox/streets-v11"
  );
  const mapContainerRef = useRef(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const [points, setPoints] = useState<Point[]>([]);
  const [areaInHectares, setAreaInHectares] = useState("");
  const [allLotes, setAllLotes] = useState([]);
  const [isParcelas, setIsParcelas] = useState(false);
  const [error, setError] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [nombre, setNombre] = useState("");
  const [open, setOpen] = useState(false);

  // Al inicio del componente MapDialog, añade este log
  useEffect(() => {
    console.log("MapDialog montado con lotes:", lotes);

    // Inspeccionar el formato de las coordenadas
    if (lotes && lotes.length > 0) {
      lotes.forEach((lote) => {
        console.log(
          `Lote ${lote.id} (${lote.name}) coordenadas:`,
          lote.coordinates
        );
      });
    }
  }, [lotes]);

  // Calcula el área del polígono usando turf y actualiza el estado
  const obtenrDatosSuperficie = () => {
    if (points.length === 4) {
      const polygonCoords = points.map((p) => [p.longitude, p.latitude]);
      // Cerrar el polígono agregando el primer punto al final
      polygonCoords.push(polygonCoords[0]);
      const polygonGeoJSON = {
        type: "Feature",
        properties: {},
        geometry: { type: "Polygon", coordinates: [polygonCoords] },
      };
      const areaInSquareMeters = turf.area(polygonGeoJSON as turf.AllGeoJSON);
      const areaHectaresCalc = areaInSquareMeters / 10000;
      setAreaInHectares(areaHectaresCalc.toFixed(2));
    } else {
      setAreaInHectares("");
    }
  };

  // Captura el clic en el mapa y añade el punto (máximo 4)
  const handleMapClick = useCallback((e) => {
    setPoints((prevPoints) => {
      if (prevPoints.length >= 4) return prevPoints;
      return [
        ...prevPoints,
        { longitude: e.lngLat.lng, latitude: e.lngLat.lat },
      ];
    });
  }, []);

  // Función para encontrar la ubicación actual
  const handleFindLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        if (mapRef.current) {
          mapRef.current.flyTo({ center: [longitude, latitude], zoom: 14 });
        }
      });
    }
  };

  // Función para eliminar puntos (resetear)
  const handleDelete = () => {
    setPoints([]);
  };

  // Función para cambiar el estilo del mapa
  const toggleMapStyle = () => {
    const mapStyles = [
      { value: "mapbox://styles/mapbox/streets-v11", label: "Calles" },
      { value: "mapbox://styles/mapbox/satellite-v9", label: "Satélite" },
      { value: "mapbox://styles/mapbox/light-v10", label: "Luz" },
      { value: "mapbox://styles/mapbox/dark-v10", label: "Oscuro" },
      { value: "mapbox://styles/mapbox/outdoors-v11", label: "Afuera" },
    ];
    const currentIndex = mapStyles.findIndex(
      (style) => style.value === mapStyle
    );
    const nextIndex = (currentIndex + 1) % mapStyles.length;
    setMapStyle(mapStyles[nextIndex].value);
  };

  const mapStyles = [
    {
      value: "mapbox://styles/mapbox/streets-v11",
      label: "Calles",
      icon: "🗺️", // Puedes cambiar el icono con un emoji
    },
    {
      value: "mapbox://styles/mapbox/satellite-v9",
      label: "Satélite",
      icon: "🛰️", // Emoji de satélite
    },
    {
      value: "mapbox://styles/mapbox/light-v10",
      label: "Luz",
      icon: "☀️", // Emoji de sol
    },
    {
      value: "mapbox://styles/mapbox/dark-v10",
      label: "Oscuro",
      icon: "🌙", // Emoji de luna
    },
    {
      value: "mapbox://styles/mapbox/outdoors-v11",
      label: "Afuera",
      icon: "🏞️", // Emoji de paisaje
    },
  ];

  // Función para inicializar el mapa (extraída para evitar duplicación)
  const initializeMap = (center: [number, number]) => {
    console.log("Inicializando mapa en:", center);
    const map = new mapboxgl.Map({
      container: mapContainerRef.current!,
      style: mapStyle,
      center: center,
      zoom: 14, // Zoom más cercano para mejor visualización
      projection: "mercator", // Prueba con 'globe' o 'mercator'
    });
    mapRef.current = map;

    // Agrega el listener para capturar clics
    map.on("click", handleMapClick);
    map.on("load", () => {
      console.log("Mapa cargado en coordenadas:", center);

      // Agregar controles básicos
      map.addControl(new mapboxgl.NavigationControl(), "top-right");
      map.addControl(new mapboxgl.FullscreenControl(), "top-left");

      // Añadir un marcador en la ubicación actual
      new mapboxgl.Marker({ color: "#FF0000" }).setLngLat(center).addTo(map);

      // Agregar botón personalizado para ir a Suardi
      class SuardiButton {
        _map: mapboxgl.Map | undefined;
        _container: HTMLElement | undefined;

        onAdd(map: mapboxgl.Map) {
          this._map = map;
          this._container = document.createElement("div");
          this._container.className = "mapboxgl-ctrl mapboxgl-ctrl-group";

          const button = document.createElement("button");
          button.className = "mapboxgl-ctrl-custom";
          button.type = "button";
          button.title = "Ir a Suardi";
          button.innerHTML = '<span style="font-size: 18px;">🏠</span>'; // Emoji de casa
          button.onclick = () => {
            const suardiCoords: [number, number] = [-61.9625, -30.5351]; // Suardi, Santa Fe
            map.flyTo({
              center: suardiCoords,
              zoom: 14,
              essential: true,
            });
          };

          this._container.appendChild(button);
          return this._container;
        }

        onRemove() {
          if (this._container && this._container.parentNode) {
            this._container.parentNode.removeChild(this._container);
          }
        }
      }

      // Agregar el botón personalizado al mapa
      map.addControl(new SuardiButton(), "top-right");

      // Dibujar lotes existentes
      if (lotes && Array.isArray(lotes) && lotes.length > 0) {
        console.log("Dibujando lotes existentes:", lotes);

        lotes.forEach((lote, index) => {
          if (lote.coordinates) {
            console.log(
              `Dibujando lote ${lote.id} (${lote.name}):`,
              lote.coordinates
            );

            // Parse coordinates if they're a string
            let coordinatesArray = lote.coordinates;
            if (!Array.isArray(coordinatesArray)) {
              try {
                // Try to parse if it's a JSON string
                coordinatesArray =
                  typeof lote.coordinates === "string"
                    ? JSON.parse(lote.coordinates)
                    : lote.coordinates;
                console.log("Coordenadas parseadas:", coordinatesArray);
              } catch (e) {
                console.error(
                  `Error al parsear coordenadas del lote ${lote.id}:`,
                  e
                );
                return; // Skip this lote if coordinates can't be parsed
              }
            }

            // Ensure coordinates is an array
            if (
              !Array.isArray(coordinatesArray) ||
              coordinatesArray.length === 0
            ) {
              console.warn(
                `El lote ${lote.id} (${lote.name}) no tiene coordenadas válidas`
              );
              return;
            }

            // Create a unique ID for each source and layer
            const sourceId = `lote-source-${lote.id}`;
            const fillLayerId = `lote-fill-${lote.id}`;
            const lineLayerId = `lote-line-${lote.id}`;

            try {
              // Verify and correct the format of coordinates if necessary
              // GeoJSON expects coordinates in [longitude, latitude] format
              const correctedCoordinates = coordinatesArray.map((coord) => {
                // If coordinates appear to be in [latitude, longitude] format
                if (
                  Array.isArray(coord) &&
                  coord.length >= 2 &&
                  Math.abs(coord[0]) <= 90 &&
                  Math.abs(coord[1]) <= 180
                ) {
                  console.log("Invirtiendo coordenadas:", coord, "->", [
                    coord[1],
                    coord[0],
                  ]);
                  return [coord[1], coord[0]]; // Invert to [longitude, latitude]
                }
                return coord; // Already in [longitude, latitude] format
              });

              // Verify that there are at least 3 points to form a polygon
              if (correctedCoordinates.length < 3) {
                console.warn(
                  `El lote ${lote.id} (${lote.name}) no tiene suficientes puntos para formar un polígono`
                );
                return;
              }

              // Create a GeoJSON for the lot
              const loteGeoJSON = {
                type: "Feature",
                properties: {
                  id: lote.id,
                  name: lote.name,
                  area: lote.area,
                },
                geometry: {
                  type: "Polygon",
                  coordinates: [correctedCoordinates],
                },
              };

              // Add the source to the map
              map.addSource(sourceId, {
                type: "geojson",
                data: loteGeoJSON as GeoJSON.Feature,
              });

              // Obtener el color para este establecimiento
              const establecimientoColor =
                getColorForEstablecimiento(establecimientoId);

              // Añadir fill layer con el color del establecimiento
              map.addLayer({
                id: fillLayerId,
                type: "fill",
                source: sourceId,
                layout: {},
                paint: {
                  "fill-color": [
                    "case",
                    ["==", ["get", "id"], selectedLoteId],
                    "#ff9900", // Color para lote seleccionado
                    establecimientoColor, // Color basado en el establecimiento
                  ],
                  "fill-opacity": 0.5,
                },
              });

              // Añadir esta propiedad al mapa para mejorar la proyección
              map.addSource("mapbox-dem", {
                type: "raster-dem",
                url: "mapbox://mapbox.mapbox-terrain-dem-v1",
                tileSize: 512,
                maxzoom: 14,
              });
              map.setTerrain({ source: "mapbox-dem", exaggeration: 1.0 });

              // Añadir line layer con el color del establecimiento
              map.addLayer({
                id: lineLayerId,
                type: "line",
                source: sourceId,
                layout: {},
                paint: {
                  "line-color": [
                    "case",
                    ["==", ["get", "id"], selectedLoteId],
                    "#ff6600", // Color para lote seleccionado
                    establecimientoColor, // Color basado en el establecimiento
                  ],
                  "line-width": 2,
                },
              });

              // Add label with lot name
              new mapboxgl.Marker({
                element: createLabelElement(lote.name, lote.area),
                anchor: "center",
              })
                .setLngLat(getCentroid(lote.coordinates) as [number, number])
                .addTo(map);
            } catch (error) {
              console.error(
                `Error al dibujar el lote ${lote.id} (${lote.name}):`,
                error
              );
            }
          } else {
            console.warn(
              `El lote ${lote.id} (${lote.name}) no tiene coordenadas válidas para dibujar`
            );
          }
        });

        // If there are lots, adjust the view to show them all
        if (lotes.length > 0) {
          try {
            // Create a bounds that includes all lots
            const bounds = new mapboxgl.LngLatBounds();
            let hasValidCoordinates = false;

            lotes.forEach((lote) => {
              // Get coordinates and parse if needed
              let loteCoordinates = lote.coordinates;
              if (!Array.isArray(loteCoordinates)) {
                try {
                  loteCoordinates =
                    typeof lote.coordinates === "string"
                      ? JSON.parse(lote.coordinates)
                      : lote.coordinates;
                } catch (e) {
                  console.error(
                    `Error al parsear coordenadas del lote ${lote.id}:`,
                    e
                  );
                  return; // Skip this lote
                }
              }

              if (
                Array.isArray(loteCoordinates) &&
                loteCoordinates.length > 0
              ) {
                loteCoordinates.forEach((coord) => {
                  // Verify that the coordinates are valid and close to Suardi
                  const correctedCoord =
                    Math.abs(coord[0]) <= 90 ? [coord[1], coord[0]] : coord;

                  // Verify that the coordinates are within a reasonable range near Suardi
                  const suardiLat = -30.5351;
                  const suardiLng = -61.9625;
                  const maxDistance = 2; // Maximum degrees of distance (approximately 200km)

                  if (
                    Math.abs(correctedCoord[1] - suardiLat) < maxDistance &&
                    Math.abs(correctedCoord[0] - suardiLng) < maxDistance
                  ) {
                    bounds.extend(correctedCoord as mapboxgl.LngLatLike);
                    hasValidCoordinates = true;
                  } else {
                    console.warn(
                      "Coordenada ignorada por estar demasiado lejos de Suardi:",
                      correctedCoord
                    );
                  }
                });
              }
            });

            // Adjust the map to show all valid lots
            if (hasValidCoordinates) {
              map.fitBounds(bounds, {
                padding: 50,
                maxZoom: 15,
              });
            } else {
              // If no valid coordinates, center on Suardi
              const suardiCoords: [number, number] = [-61.9625, -30.5351];
              map.flyTo({
                center: suardiCoords,
                zoom: 14,
                essential: true,
              });
            }
          } catch (error) {
            console.error("Error al ajustar la vista del mapa:", error);
            // In case of error, center on Suardi
            const suardiCoords: [number, number] = [-61.9625, -30.5351];
            map.flyTo({
              center: suardiCoords,
              zoom: 14,
              essential: true,
            });
          }
        } else {
          // If no lots, center on Suardi
          const suardiCoords: [number, number] = [-61.9625, -30.5351];
          map.flyTo({
            center: suardiCoords,
            zoom: 14,
            essential: true,
          });
        }
      }
    });
  };

  // Función auxiliar para crear un elemento de etiqueta
  const createLabelElement = (name: string, area: number) => {
    const el = document.createElement("div");
    el.className = "lote-label";
    el.style.backgroundColor = "white";
    el.style.border = `1px solid ${getColorForEstablecimiento(
      establecimientoId
    )}`;
    el.style.borderRadius = "4px";
    el.style.padding = "4px 8px";
    el.style.fontSize = "12px";
    el.style.fontWeight = "bold";
    el.style.whiteSpace = "nowrap";
    el.style.pointerEvents = "none";
    el.innerHTML = `${name} (${area.toFixed(2)} ha)`;
    return el;
  };

  // Función auxiliar para obtener el centroide de un polígono
  const getCentroid = (coordinates: any): [number, number] => {
    // Parse coordinates if they're a string
    let coordinatesArray = coordinates;
    if (!Array.isArray(coordinatesArray)) {
      try {
        coordinatesArray = JSON.parse(coordinates);
      } catch (e) {
        console.error("Error al parsear coordenadas para centroide:", e);
        return [-61.9625, -30.5351]; // Default to Suardi coordinates
      }
    }

    // Ensure coordinates is an array
    if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {
      return [-61.9625, -30.5351]; // Default to Suardi coordinates
    }

    // Verificar si las coordenadas están en formato [latitud, longitud]
    const correctedCoords = coordinatesArray.map((coord) => {
      if (
        Array.isArray(coord) &&
        coord.length >= 2 &&
        Math.abs(coord[0]) <= 90 &&
        Math.abs(coord[1]) <= 180
      ) {
        return [coord[1], coord[0]]; // Convertir a [longitud, latitud]
      }
      return coord;
    });

    let sumX = 0;
    let sumY = 0;

    correctedCoords.forEach((coord) => {
      sumX += coord[0];
      sumY += coord[1];
    });

    return [sumX / correctedCoords.length, sumY / correctedCoords.length];
  };

  // Inicializa el mapa cuando se abre el diálogo
  useEffect(() => {
    if (!openDialog) return;

    // Reinicia datos al abrir el diálogo
    setPoints([]);
    setAreaInHectares("");

    // Depuración de lotes
    console.log("MapDialog montado con lotes:", lotes);

    if (lotes && lotes.length > 0) {
      lotes.forEach((lote) => {
        console.log(
          `Lote ${lote.id} (${lote.name}) coordenadas:`,
          lote.coordinates
        );
        // Verificar si las coordenadas son válidas
        if (
          !lote.coordinates ||
          !Array.isArray(lote.coordinates) ||
          lote.coordinates.length === 0
        ) {
          console.warn(
            `Lote ${lote.id} (${lote.name}) no tiene coordenadas válidas`
          );
        }
      });
    } else {
      console.log("No hay lotes para mostrar en el mapa");
    }

    const timer = setTimeout(() => {
      if (mapContainerRef.current && !mapRef.current) {
        mapboxgl.accessToken =
          "pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A";

        // Inicializar directamente en Suardi, Santa Fe
        const suardiCoords: [number, number] = [-61.9625, -30.5351]; // Suardi, Santa Fe
        console.log("Inicializando mapa en Suardi, Santa Fe");
        initializeMap(suardiCoords);
      }
    }, 500);

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      clearTimeout(timer);
    };
  }, [openDialog, mapStyle, lotes, handleMapClick, establecimientoId]);

  // Actualiza marcadores y polígono cuando se actualicen los puntos
  useEffect(() => {
    if (!mapRef.current) return;

    // Properly type the markers array
    markersRef.current.forEach((marker: mapboxgl.Marker) => marker.remove());
    markersRef.current = [];

    // Create a marker for each point
    points.forEach((p) => {
      const marker = new mapboxgl.Marker({
        scale: 0.6,
      })
        .setLngLat([p.longitude, p.latitude])
        .addTo(mapRef.current!); // Use non-null assertion since we checked above

      markersRef.current.push(marker);
    });

    // If there are 4 points, draw the polygon
    if (points.length === 4) {
      const polygonCoords = points.map((p) => [p.longitude, p.latitude]);
      polygonCoords.push(polygonCoords[0]); // Close the polygon
      const polygonGeoJSON: GeoJSON.Feature<GeoJSON.Polygon> = {
        type: "Feature",
        properties: {},
        geometry: {
          type: "Polygon",
          coordinates: [polygonCoords],
        },
      };

      if (mapRef.current.getSource("polygon")) {
        const source = mapRef.current.getSource(
          "polygon"
        ) as mapboxgl.GeoJSONSource;
        source.setData(polygonGeoJSON);
      } else {
        mapRef.current.addSource("polygon", {
          type: "geojson",
          data: polygonGeoJSON,
        });
        mapRef.current.addLayer({
          id: "polygon-fill",
          type: "fill",
          source: "polygon",
          layout: {},
          paint: {
            "fill-color": "#0080ff",
            "fill-opacity": 0.5,
          },
        });
        mapRef.current.addLayer({
          id: "polygon-outline",
          type: "line",
          source: "polygon",
          layout: {},
          paint: {
            "line-color": "#0080ff",
            "line-width": 2,
          },
        });
      }
    } else {
      // Remove polygon if it exists
      if (mapRef.current.getLayer("polygon-fill")) {
        mapRef.current.removeLayer("polygon-fill");
      }
      if (mapRef.current.getLayer("polygon-outline")) {
        mapRef.current.removeLayer("polygon-outline");
      }
      if (mapRef.current.getSource("polygon")) {
        mapRef.current.removeSource("polygon");
      }
    }
  }, [points]);

  // Recalcula el área cada vez que se actualizan los puntos
  useEffect(() => {
    obtenrDatosSuperficie();
  }, [points]);

  // Genera el string con todas las coordenadas, separándolas con " | "
  const coordinatesString = points
    .map((p) => `${p.longitude.toFixed(6)}, ${p.latitude.toFixed(6)}`)
    .join(" | ");

  // Guarda los datos y resetea el estado
  const handleLoadValues = () => {
    if (points.length >= 4) {
      obtenrDatosSuperficie();

      const data = {
        points: points.map((p) => ({
          latitude: p.latitude,
          longitude: p.longitude,
        })),
        areaInHectares: Number(areaInHectares),
        name: nombre,
      };

      onSave(data); // Pass data to parent component without showing alert
      onClose();
    } else {
      // Optional: Add error handling for incomplete polygon
      alert("Por favor, marque 4 puntos en el mapa para definir el lote.");
    }
  };

  // Añade un manejador de cambios para el TextField
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNombre(e.target.value);
  };

  // Añade esta función para generar colores basados en el ID del establecimiento
  const getColorForEstablecimiento = (establecimientoId: string) => {
    // Lista de colores distintos para diferentes establecimientos
    const colors = [
      "#3498db", // Azul
      "#e74c3c", // Rojo
      "#2ecc71", // Verde
      "#f39c12", // Naranja
      "#9b59b6", // Púrpura
      "#1abc9c", // Turquesa
      "#d35400", // Naranja oscuro
      "#27ae60", // Verde esmeralda
      "#c0392b", // Rojo oscuro
      "#8e44ad", // Púrpura oscuro
    ];

    // Convertir el ID a un número para usarlo como índice
    let numericId = 0;
    if (establecimientoId) {
      // Si es un UUID, usar la suma de los códigos de caracteres
      if (establecimientoId.includes("-")) {
        numericId = establecimientoId
          .split("")
          .reduce((acc, char) => acc + char.charCodeAt(0), 0);
      } else {
        // Si es numérico, convertirlo
        numericId = parseInt(establecimientoId, 10) || 0;
      }
    }

    // Usar el módulo para obtener un índice dentro del rango de colores
    const colorIndex = numericId % colors.length;
    return colors[colorIndex];
  };

  return (
    <Dialog
      open={openDialog}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        "& .MuiPaper-root": {
          transition: "width 0.3s ease, height 0.3s ease",
          width: isMaximized ? "100%" : "auto",
          height: isMaximized ? "100%" : "auto",
          maxWidth: isMaximized ? "100%" : "900px", // Reducido de 1200px a 900px
          maxHeight: isMaximized ? "100%" : "800px",
        },
      }}
    >
      <DialogTitle>
        Marcar Coordenadas
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Box
          sx={{
            backgroundColor: "#f0f4f8",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginTop: "15px",
            marginBottom: "15px",
          }}
        >
          <MapIcon sx={{ color: "#5C6BC0", fontSize: "30px" }} />
          <Typography
            variant="body1"
            sx={{ color: "#555", fontSize: "16px", fontWeight: 400 }}
          >
            Marque 4 puntos en el mapa para definir el lote y/o parcela. Ingrese
            un nombre y el sistema calculará automáticamente el área. Use el
            botón superior derecho para cambiar la vista.
          </Typography>
        </Box>

        <div
          ref={mapContainerRef}
          style={{
            position: "relative",
            height: "500px",
            border: "2px solid black",
          }}
        ></div>

        {/* Panel para mostrar las coordenadas y la superficie */}
        <Box mt={2} display="flex" flexDirection="column" gap={2}>
          <TextField
            label="Nombre del Lote"
            variant="outlined"
            value={nombre}
            onChange={handleNameChange}
            InputProps={{
              sx: { fontSize: "0.875rem" },
            }}
            sx={{ width: "300px" }}
          />
          <Box display="flex" gap={2}>
            <TextField
              label="Coordenadas"
              value={coordinatesString}
              InputProps={{ readOnly: true }}
              disabled
              sx={{ flex: 3 }}
            />
            <TextField
              label="Superficie (ha)"
              value={areaInHectares ? `${areaInHectares} ha` : ""}
              InputProps={{ readOnly: true }}
              disabled
              sx={{ flex: 1 }}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleLoadValues} color="primary">
          Cargar Valores
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MapDialog;
