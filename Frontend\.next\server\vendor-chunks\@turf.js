"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@turf";
exports.ids = ["vendor-chunks/@turf"];
exports.modules = {

/***/ "(ssr)/./node_modules/@turf/area/dist/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@turf/area/dist/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   area: () => (/* binding */ area),\n/* harmony export */   \"default\": () => (/* binding */ turf_area_default)\n/* harmony export */ });\n/* harmony import */ var _turf_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @turf/helpers */ \"(ssr)/./node_modules/@turf/helpers/dist/esm/index.js\");\n/* harmony import */ var _turf_meta__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @turf/meta */ \"(ssr)/./node_modules/@turf/meta/dist/esm/index.js\");\n// index.ts\n\n\nfunction area(geojson) {\n  return (0,_turf_meta__WEBPACK_IMPORTED_MODULE_0__.geomReduce)(\n    geojson,\n    (value, geom) => {\n      return value + calculateArea(geom);\n    },\n    0\n  );\n}\nfunction calculateArea(geom) {\n  let total = 0;\n  let i;\n  switch (geom.type) {\n    case \"Polygon\":\n      return polygonArea(geom.coordinates);\n    case \"MultiPolygon\":\n      for (i = 0; i < geom.coordinates.length; i++) {\n        total += polygonArea(geom.coordinates[i]);\n      }\n      return total;\n    case \"Point\":\n    case \"MultiPoint\":\n    case \"LineString\":\n    case \"MultiLineString\":\n      return 0;\n  }\n  return 0;\n}\nfunction polygonArea(coords) {\n  let total = 0;\n  if (coords && coords.length > 0) {\n    total += Math.abs(ringArea(coords[0]));\n    for (let i = 1; i < coords.length; i++) {\n      total -= Math.abs(ringArea(coords[i]));\n    }\n  }\n  return total;\n}\nvar FACTOR = _turf_helpers__WEBPACK_IMPORTED_MODULE_1__.earthRadius * _turf_helpers__WEBPACK_IMPORTED_MODULE_1__.earthRadius / 2;\nvar PI_OVER_180 = Math.PI / 180;\nfunction ringArea(coords) {\n  const coordsLength = coords.length - 1;\n  if (coordsLength <= 2)\n    return 0;\n  let total = 0;\n  let i = 0;\n  while (i < coordsLength) {\n    const lower = coords[i];\n    const middle = coords[i + 1 === coordsLength ? 0 : i + 1];\n    const upper = coords[i + 2 >= coordsLength ? (i + 2) % coordsLength : i + 2];\n    const lowerX = lower[0] * PI_OVER_180;\n    const middleY = middle[1] * PI_OVER_180;\n    const upperX = upper[0] * PI_OVER_180;\n    total += (upperX - lowerX) * Math.sin(middleY);\n    i++;\n  }\n  return total * FACTOR;\n}\nvar turf_area_default = area;\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@turf/area/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@turf/helpers/dist/esm/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@turf/helpers/dist/esm/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areaFactors: () => (/* binding */ areaFactors),\n/* harmony export */   azimuthToBearing: () => (/* binding */ azimuthToBearing),\n/* harmony export */   bearingToAzimuth: () => (/* binding */ bearingToAzimuth),\n/* harmony export */   convertArea: () => (/* binding */ convertArea),\n/* harmony export */   convertLength: () => (/* binding */ convertLength),\n/* harmony export */   degreesToRadians: () => (/* binding */ degreesToRadians),\n/* harmony export */   earthRadius: () => (/* binding */ earthRadius),\n/* harmony export */   factors: () => (/* binding */ factors),\n/* harmony export */   feature: () => (/* binding */ feature),\n/* harmony export */   featureCollection: () => (/* binding */ featureCollection),\n/* harmony export */   geometry: () => (/* binding */ geometry),\n/* harmony export */   geometryCollection: () => (/* binding */ geometryCollection),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   lengthToDegrees: () => (/* binding */ lengthToDegrees),\n/* harmony export */   lengthToRadians: () => (/* binding */ lengthToRadians),\n/* harmony export */   lineString: () => (/* binding */ lineString),\n/* harmony export */   lineStrings: () => (/* binding */ lineStrings),\n/* harmony export */   multiLineString: () => (/* binding */ multiLineString),\n/* harmony export */   multiPoint: () => (/* binding */ multiPoint),\n/* harmony export */   multiPolygon: () => (/* binding */ multiPolygon),\n/* harmony export */   point: () => (/* binding */ point),\n/* harmony export */   points: () => (/* binding */ points),\n/* harmony export */   polygon: () => (/* binding */ polygon),\n/* harmony export */   polygons: () => (/* binding */ polygons),\n/* harmony export */   radiansToDegrees: () => (/* binding */ radiansToDegrees),\n/* harmony export */   radiansToLength: () => (/* binding */ radiansToLength),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   validateBBox: () => (/* binding */ validateBBox),\n/* harmony export */   validateId: () => (/* binding */ validateId)\n/* harmony export */ });\n// index.ts\nvar earthRadius = 63710088e-1;\nvar factors = {\n  centimeters: earthRadius * 100,\n  centimetres: earthRadius * 100,\n  degrees: 360 / (2 * Math.PI),\n  feet: earthRadius * 3.28084,\n  inches: earthRadius * 39.37,\n  kilometers: earthRadius / 1e3,\n  kilometres: earthRadius / 1e3,\n  meters: earthRadius,\n  metres: earthRadius,\n  miles: earthRadius / 1609.344,\n  millimeters: earthRadius * 1e3,\n  millimetres: earthRadius * 1e3,\n  nauticalmiles: earthRadius / 1852,\n  radians: 1,\n  yards: earthRadius * 1.0936\n};\nvar areaFactors = {\n  acres: 247105e-9,\n  centimeters: 1e4,\n  centimetres: 1e4,\n  feet: 10.763910417,\n  hectares: 1e-4,\n  inches: 1550.003100006,\n  kilometers: 1e-6,\n  kilometres: 1e-6,\n  meters: 1,\n  metres: 1,\n  miles: 386e-9,\n  nauticalmiles: 29155334959812285e-23,\n  millimeters: 1e6,\n  millimetres: 1e6,\n  yards: 1.195990046\n};\nfunction feature(geom, properties, options = {}) {\n  const feat = { type: \"Feature\" };\n  if (options.id === 0 || options.id) {\n    feat.id = options.id;\n  }\n  if (options.bbox) {\n    feat.bbox = options.bbox;\n  }\n  feat.properties = properties || {};\n  feat.geometry = geom;\n  return feat;\n}\nfunction geometry(type, coordinates, _options = {}) {\n  switch (type) {\n    case \"Point\":\n      return point(coordinates).geometry;\n    case \"LineString\":\n      return lineString(coordinates).geometry;\n    case \"Polygon\":\n      return polygon(coordinates).geometry;\n    case \"MultiPoint\":\n      return multiPoint(coordinates).geometry;\n    case \"MultiLineString\":\n      return multiLineString(coordinates).geometry;\n    case \"MultiPolygon\":\n      return multiPolygon(coordinates).geometry;\n    default:\n      throw new Error(type + \" is invalid\");\n  }\n}\nfunction point(coordinates, properties, options = {}) {\n  if (!coordinates) {\n    throw new Error(\"coordinates is required\");\n  }\n  if (!Array.isArray(coordinates)) {\n    throw new Error(\"coordinates must be an Array\");\n  }\n  if (coordinates.length < 2) {\n    throw new Error(\"coordinates must be at least 2 numbers long\");\n  }\n  if (!isNumber(coordinates[0]) || !isNumber(coordinates[1])) {\n    throw new Error(\"coordinates must contain numbers\");\n  }\n  const geom = {\n    type: \"Point\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction points(coordinates, properties, options = {}) {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return point(coords, properties);\n    }),\n    options\n  );\n}\nfunction polygon(coordinates, properties, options = {}) {\n  for (const ring of coordinates) {\n    if (ring.length < 4) {\n      throw new Error(\n        \"Each LinearRing of a Polygon must have 4 or more Positions.\"\n      );\n    }\n    if (ring[ring.length - 1].length !== ring[0].length) {\n      throw new Error(\"First and last Position are not equivalent.\");\n    }\n    for (let j = 0; j < ring[ring.length - 1].length; j++) {\n      if (ring[ring.length - 1][j] !== ring[0][j]) {\n        throw new Error(\"First and last Position are not equivalent.\");\n      }\n    }\n  }\n  const geom = {\n    type: \"Polygon\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction polygons(coordinates, properties, options = {}) {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return polygon(coords, properties);\n    }),\n    options\n  );\n}\nfunction lineString(coordinates, properties, options = {}) {\n  if (coordinates.length < 2) {\n    throw new Error(\"coordinates must be an array of two or more positions\");\n  }\n  const geom = {\n    type: \"LineString\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction lineStrings(coordinates, properties, options = {}) {\n  return featureCollection(\n    coordinates.map((coords) => {\n      return lineString(coords, properties);\n    }),\n    options\n  );\n}\nfunction featureCollection(features, options = {}) {\n  const fc = { type: \"FeatureCollection\" };\n  if (options.id) {\n    fc.id = options.id;\n  }\n  if (options.bbox) {\n    fc.bbox = options.bbox;\n  }\n  fc.features = features;\n  return fc;\n}\nfunction multiLineString(coordinates, properties, options = {}) {\n  const geom = {\n    type: \"MultiLineString\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction multiPoint(coordinates, properties, options = {}) {\n  const geom = {\n    type: \"MultiPoint\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction multiPolygon(coordinates, properties, options = {}) {\n  const geom = {\n    type: \"MultiPolygon\",\n    coordinates\n  };\n  return feature(geom, properties, options);\n}\nfunction geometryCollection(geometries, properties, options = {}) {\n  const geom = {\n    type: \"GeometryCollection\",\n    geometries\n  };\n  return feature(geom, properties, options);\n}\nfunction round(num, precision = 0) {\n  if (precision && !(precision >= 0)) {\n    throw new Error(\"precision must be a positive number\");\n  }\n  const multiplier = Math.pow(10, precision || 0);\n  return Math.round(num * multiplier) / multiplier;\n}\nfunction radiansToLength(radians, units = \"kilometers\") {\n  const factor = factors[units];\n  if (!factor) {\n    throw new Error(units + \" units is invalid\");\n  }\n  return radians * factor;\n}\nfunction lengthToRadians(distance, units = \"kilometers\") {\n  const factor = factors[units];\n  if (!factor) {\n    throw new Error(units + \" units is invalid\");\n  }\n  return distance / factor;\n}\nfunction lengthToDegrees(distance, units) {\n  return radiansToDegrees(lengthToRadians(distance, units));\n}\nfunction bearingToAzimuth(bearing) {\n  let angle = bearing % 360;\n  if (angle < 0) {\n    angle += 360;\n  }\n  return angle;\n}\nfunction azimuthToBearing(angle) {\n  angle = angle % 360;\n  if (angle > 0)\n    return angle > 180 ? angle - 360 : angle;\n  return angle < -180 ? angle + 360 : angle;\n}\nfunction radiansToDegrees(radians) {\n  const degrees = radians % (2 * Math.PI);\n  return degrees * 180 / Math.PI;\n}\nfunction degreesToRadians(degrees) {\n  const radians = degrees % 360;\n  return radians * Math.PI / 180;\n}\nfunction convertLength(length, originalUnit = \"kilometers\", finalUnit = \"kilometers\") {\n  if (!(length >= 0)) {\n    throw new Error(\"length must be a positive number\");\n  }\n  return radiansToLength(lengthToRadians(length, originalUnit), finalUnit);\n}\nfunction convertArea(area, originalUnit = \"meters\", finalUnit = \"kilometers\") {\n  if (!(area >= 0)) {\n    throw new Error(\"area must be a positive number\");\n  }\n  const startFactor = areaFactors[originalUnit];\n  if (!startFactor) {\n    throw new Error(\"invalid original units\");\n  }\n  const finalFactor = areaFactors[finalUnit];\n  if (!finalFactor) {\n    throw new Error(\"invalid final units\");\n  }\n  return area / startFactor * finalFactor;\n}\nfunction isNumber(num) {\n  return !isNaN(num) && num !== null && !Array.isArray(num);\n}\nfunction isObject(input) {\n  return input !== null && typeof input === \"object\" && !Array.isArray(input);\n}\nfunction validateBBox(bbox) {\n  if (!bbox) {\n    throw new Error(\"bbox is required\");\n  }\n  if (!Array.isArray(bbox)) {\n    throw new Error(\"bbox must be an Array\");\n  }\n  if (bbox.length !== 4 && bbox.length !== 6) {\n    throw new Error(\"bbox must be an Array of 4 or 6 numbers\");\n  }\n  bbox.forEach((num) => {\n    if (!isNumber(num)) {\n      throw new Error(\"bbox must only contain numbers\");\n    }\n  });\n}\nfunction validateId(id) {\n  if (!id) {\n    throw new Error(\"id is required\");\n  }\n  if ([\"string\", \"number\"].indexOf(typeof id) === -1) {\n    throw new Error(\"id must be a number or a string\");\n  }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@turf/helpers/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@turf/meta/dist/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@turf/meta/dist/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coordAll: () => (/* binding */ coordAll),\n/* harmony export */   coordEach: () => (/* binding */ coordEach),\n/* harmony export */   coordReduce: () => (/* binding */ coordReduce),\n/* harmony export */   featureEach: () => (/* binding */ featureEach),\n/* harmony export */   featureReduce: () => (/* binding */ featureReduce),\n/* harmony export */   findPoint: () => (/* binding */ findPoint),\n/* harmony export */   findSegment: () => (/* binding */ findSegment),\n/* harmony export */   flattenEach: () => (/* binding */ flattenEach),\n/* harmony export */   flattenReduce: () => (/* binding */ flattenReduce),\n/* harmony export */   geomEach: () => (/* binding */ geomEach),\n/* harmony export */   geomReduce: () => (/* binding */ geomReduce),\n/* harmony export */   lineEach: () => (/* binding */ lineEach),\n/* harmony export */   lineReduce: () => (/* binding */ lineReduce),\n/* harmony export */   propEach: () => (/* binding */ propEach),\n/* harmony export */   propReduce: () => (/* binding */ propReduce),\n/* harmony export */   segmentEach: () => (/* binding */ segmentEach),\n/* harmony export */   segmentReduce: () => (/* binding */ segmentReduce)\n/* harmony export */ });\n/* harmony import */ var _turf_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @turf/helpers */ \"(ssr)/./node_modules/@turf/helpers/dist/esm/index.js\");\n// index.js\n\nfunction coordEach(geojson, callback, excludeWrapCoord) {\n  if (geojson === null)\n    return;\n  var j, k, l, geometry, stopG, coords, geometryMaybeCollection, wrapShrink = 0, coordIndex = 0, isGeometryCollection, type = geojson.type, isFeatureCollection = type === \"FeatureCollection\", isFeature = type === \"Feature\", stop = isFeatureCollection ? geojson.features.length : 1;\n  for (var featureIndex = 0; featureIndex < stop; featureIndex++) {\n    geometryMaybeCollection = isFeatureCollection ? geojson.features[featureIndex].geometry : isFeature ? geojson.geometry : geojson;\n    isGeometryCollection = geometryMaybeCollection ? geometryMaybeCollection.type === \"GeometryCollection\" : false;\n    stopG = isGeometryCollection ? geometryMaybeCollection.geometries.length : 1;\n    for (var geomIndex = 0; geomIndex < stopG; geomIndex++) {\n      var multiFeatureIndex = 0;\n      var geometryIndex = 0;\n      geometry = isGeometryCollection ? geometryMaybeCollection.geometries[geomIndex] : geometryMaybeCollection;\n      if (geometry === null)\n        continue;\n      coords = geometry.coordinates;\n      var geomType = geometry.type;\n      wrapShrink = excludeWrapCoord && (geomType === \"Polygon\" || geomType === \"MultiPolygon\") ? 1 : 0;\n      switch (geomType) {\n        case null:\n          break;\n        case \"Point\":\n          if (callback(\n            coords,\n            coordIndex,\n            featureIndex,\n            multiFeatureIndex,\n            geometryIndex\n          ) === false)\n            return false;\n          coordIndex++;\n          multiFeatureIndex++;\n          break;\n        case \"LineString\":\n        case \"MultiPoint\":\n          for (j = 0; j < coords.length; j++) {\n            if (callback(\n              coords[j],\n              coordIndex,\n              featureIndex,\n              multiFeatureIndex,\n              geometryIndex\n            ) === false)\n              return false;\n            coordIndex++;\n            if (geomType === \"MultiPoint\")\n              multiFeatureIndex++;\n          }\n          if (geomType === \"LineString\")\n            multiFeatureIndex++;\n          break;\n        case \"Polygon\":\n        case \"MultiLineString\":\n          for (j = 0; j < coords.length; j++) {\n            for (k = 0; k < coords[j].length - wrapShrink; k++) {\n              if (callback(\n                coords[j][k],\n                coordIndex,\n                featureIndex,\n                multiFeatureIndex,\n                geometryIndex\n              ) === false)\n                return false;\n              coordIndex++;\n            }\n            if (geomType === \"MultiLineString\")\n              multiFeatureIndex++;\n            if (geomType === \"Polygon\")\n              geometryIndex++;\n          }\n          if (geomType === \"Polygon\")\n            multiFeatureIndex++;\n          break;\n        case \"MultiPolygon\":\n          for (j = 0; j < coords.length; j++) {\n            geometryIndex = 0;\n            for (k = 0; k < coords[j].length; k++) {\n              for (l = 0; l < coords[j][k].length - wrapShrink; l++) {\n                if (callback(\n                  coords[j][k][l],\n                  coordIndex,\n                  featureIndex,\n                  multiFeatureIndex,\n                  geometryIndex\n                ) === false)\n                  return false;\n                coordIndex++;\n              }\n              geometryIndex++;\n            }\n            multiFeatureIndex++;\n          }\n          break;\n        case \"GeometryCollection\":\n          for (j = 0; j < geometry.geometries.length; j++)\n            if (coordEach(geometry.geometries[j], callback, excludeWrapCoord) === false)\n              return false;\n          break;\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n  }\n}\nfunction coordReduce(geojson, callback, initialValue, excludeWrapCoord) {\n  var previousValue = initialValue;\n  coordEach(\n    geojson,\n    function(currentCoord, coordIndex, featureIndex, multiFeatureIndex, geometryIndex) {\n      if (coordIndex === 0 && initialValue === void 0)\n        previousValue = currentCoord;\n      else\n        previousValue = callback(\n          previousValue,\n          currentCoord,\n          coordIndex,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    },\n    excludeWrapCoord\n  );\n  return previousValue;\n}\nfunction propEach(geojson, callback) {\n  var i;\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      for (i = 0; i < geojson.features.length; i++) {\n        if (callback(geojson.features[i].properties, i) === false)\n          break;\n      }\n      break;\n    case \"Feature\":\n      callback(geojson.properties, 0);\n      break;\n  }\n}\nfunction propReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  propEach(geojson, function(currentProperties, featureIndex) {\n    if (featureIndex === 0 && initialValue === void 0)\n      previousValue = currentProperties;\n    else\n      previousValue = callback(previousValue, currentProperties, featureIndex);\n  });\n  return previousValue;\n}\nfunction featureEach(geojson, callback) {\n  if (geojson.type === \"Feature\") {\n    callback(geojson, 0);\n  } else if (geojson.type === \"FeatureCollection\") {\n    for (var i = 0; i < geojson.features.length; i++) {\n      if (callback(geojson.features[i], i) === false)\n        break;\n    }\n  }\n}\nfunction featureReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  featureEach(geojson, function(currentFeature, featureIndex) {\n    if (featureIndex === 0 && initialValue === void 0)\n      previousValue = currentFeature;\n    else\n      previousValue = callback(previousValue, currentFeature, featureIndex);\n  });\n  return previousValue;\n}\nfunction coordAll(geojson) {\n  var coords = [];\n  coordEach(geojson, function(coord) {\n    coords.push(coord);\n  });\n  return coords;\n}\nfunction geomEach(geojson, callback) {\n  var i, j, g, geometry, stopG, geometryMaybeCollection, isGeometryCollection, featureProperties, featureBBox, featureId, featureIndex = 0, isFeatureCollection = geojson.type === \"FeatureCollection\", isFeature = geojson.type === \"Feature\", stop = isFeatureCollection ? geojson.features.length : 1;\n  for (i = 0; i < stop; i++) {\n    geometryMaybeCollection = isFeatureCollection ? geojson.features[i].geometry : isFeature ? geojson.geometry : geojson;\n    featureProperties = isFeatureCollection ? geojson.features[i].properties : isFeature ? geojson.properties : {};\n    featureBBox = isFeatureCollection ? geojson.features[i].bbox : isFeature ? geojson.bbox : void 0;\n    featureId = isFeatureCollection ? geojson.features[i].id : isFeature ? geojson.id : void 0;\n    isGeometryCollection = geometryMaybeCollection ? geometryMaybeCollection.type === \"GeometryCollection\" : false;\n    stopG = isGeometryCollection ? geometryMaybeCollection.geometries.length : 1;\n    for (g = 0; g < stopG; g++) {\n      geometry = isGeometryCollection ? geometryMaybeCollection.geometries[g] : geometryMaybeCollection;\n      if (geometry === null) {\n        if (callback(\n          null,\n          featureIndex,\n          featureProperties,\n          featureBBox,\n          featureId\n        ) === false)\n          return false;\n        continue;\n      }\n      switch (geometry.type) {\n        case \"Point\":\n        case \"LineString\":\n        case \"MultiPoint\":\n        case \"Polygon\":\n        case \"MultiLineString\":\n        case \"MultiPolygon\": {\n          if (callback(\n            geometry,\n            featureIndex,\n            featureProperties,\n            featureBBox,\n            featureId\n          ) === false)\n            return false;\n          break;\n        }\n        case \"GeometryCollection\": {\n          for (j = 0; j < geometry.geometries.length; j++) {\n            if (callback(\n              geometry.geometries[j],\n              featureIndex,\n              featureProperties,\n              featureBBox,\n              featureId\n            ) === false)\n              return false;\n          }\n          break;\n        }\n        default:\n          throw new Error(\"Unknown Geometry Type\");\n      }\n    }\n    featureIndex++;\n  }\n}\nfunction geomReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  geomEach(\n    geojson,\n    function(currentGeometry, featureIndex, featureProperties, featureBBox, featureId) {\n      if (featureIndex === 0 && initialValue === void 0)\n        previousValue = currentGeometry;\n      else\n        previousValue = callback(\n          previousValue,\n          currentGeometry,\n          featureIndex,\n          featureProperties,\n          featureBBox,\n          featureId\n        );\n    }\n  );\n  return previousValue;\n}\nfunction flattenEach(geojson, callback) {\n  geomEach(geojson, function(geometry, featureIndex, properties, bbox, id) {\n    var type = geometry === null ? null : geometry.type;\n    switch (type) {\n      case null:\n      case \"Point\":\n      case \"LineString\":\n      case \"Polygon\":\n        if (callback(\n          (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.feature)(geometry, properties, { bbox, id }),\n          featureIndex,\n          0\n        ) === false)\n          return false;\n        return;\n    }\n    var geomType;\n    switch (type) {\n      case \"MultiPoint\":\n        geomType = \"Point\";\n        break;\n      case \"MultiLineString\":\n        geomType = \"LineString\";\n        break;\n      case \"MultiPolygon\":\n        geomType = \"Polygon\";\n        break;\n    }\n    for (var multiFeatureIndex = 0; multiFeatureIndex < geometry.coordinates.length; multiFeatureIndex++) {\n      var coordinate = geometry.coordinates[multiFeatureIndex];\n      var geom = {\n        type: geomType,\n        coordinates: coordinate\n      };\n      if (callback((0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.feature)(geom, properties), featureIndex, multiFeatureIndex) === false)\n        return false;\n    }\n  });\n}\nfunction flattenReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  flattenEach(\n    geojson,\n    function(currentFeature, featureIndex, multiFeatureIndex) {\n      if (featureIndex === 0 && multiFeatureIndex === 0 && initialValue === void 0)\n        previousValue = currentFeature;\n      else\n        previousValue = callback(\n          previousValue,\n          currentFeature,\n          featureIndex,\n          multiFeatureIndex\n        );\n    }\n  );\n  return previousValue;\n}\nfunction segmentEach(geojson, callback) {\n  flattenEach(geojson, function(feature2, featureIndex, multiFeatureIndex) {\n    var segmentIndex = 0;\n    if (!feature2.geometry)\n      return;\n    var type = feature2.geometry.type;\n    if (type === \"Point\" || type === \"MultiPoint\")\n      return;\n    var previousCoords;\n    var previousFeatureIndex = 0;\n    var previousMultiIndex = 0;\n    var prevGeomIndex = 0;\n    if (coordEach(\n      feature2,\n      function(currentCoord, coordIndex, featureIndexCoord, multiPartIndexCoord, geometryIndex) {\n        if (previousCoords === void 0 || featureIndex > previousFeatureIndex || multiPartIndexCoord > previousMultiIndex || geometryIndex > prevGeomIndex) {\n          previousCoords = currentCoord;\n          previousFeatureIndex = featureIndex;\n          previousMultiIndex = multiPartIndexCoord;\n          prevGeomIndex = geometryIndex;\n          segmentIndex = 0;\n          return;\n        }\n        var currentSegment = (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(\n          [previousCoords, currentCoord],\n          feature2.properties\n        );\n        if (callback(\n          currentSegment,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex,\n          segmentIndex\n        ) === false)\n          return false;\n        segmentIndex++;\n        previousCoords = currentCoord;\n      }\n    ) === false)\n      return false;\n  });\n}\nfunction segmentReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  var started = false;\n  segmentEach(\n    geojson,\n    function(currentSegment, featureIndex, multiFeatureIndex, geometryIndex, segmentIndex) {\n      if (started === false && initialValue === void 0)\n        previousValue = currentSegment;\n      else\n        previousValue = callback(\n          previousValue,\n          currentSegment,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex,\n          segmentIndex\n        );\n      started = true;\n    }\n  );\n  return previousValue;\n}\nfunction lineEach(geojson, callback) {\n  if (!geojson)\n    throw new Error(\"geojson is required\");\n  flattenEach(geojson, function(feature2, featureIndex, multiFeatureIndex) {\n    if (feature2.geometry === null)\n      return;\n    var type = feature2.geometry.type;\n    var coords = feature2.geometry.coordinates;\n    switch (type) {\n      case \"LineString\":\n        if (callback(feature2, featureIndex, multiFeatureIndex, 0, 0) === false)\n          return false;\n        break;\n      case \"Polygon\":\n        for (var geometryIndex = 0; geometryIndex < coords.length; geometryIndex++) {\n          if (callback(\n            (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(coords[geometryIndex], feature2.properties),\n            featureIndex,\n            multiFeatureIndex,\n            geometryIndex\n          ) === false)\n            return false;\n        }\n        break;\n    }\n  });\n}\nfunction lineReduce(geojson, callback, initialValue) {\n  var previousValue = initialValue;\n  lineEach(\n    geojson,\n    function(currentLine, featureIndex, multiFeatureIndex, geometryIndex) {\n      if (featureIndex === 0 && initialValue === void 0)\n        previousValue = currentLine;\n      else\n        previousValue = callback(\n          previousValue,\n          currentLine,\n          featureIndex,\n          multiFeatureIndex,\n          geometryIndex\n        );\n    }\n  );\n  return previousValue;\n}\nfunction findSegment(geojson, options) {\n  options = options || {};\n  if (!(0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.isObject)(options))\n    throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var segmentIndex = options.segmentIndex || 0;\n  var properties = options.properties;\n  var geometry;\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n  if (geometry === null)\n    return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n      if (segmentIndex < 0)\n        segmentIndex = coords.length + segmentIndex - 1;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(\n        [coords[segmentIndex], coords[segmentIndex + 1]],\n        properties,\n        options\n      );\n    case \"Polygon\":\n      if (geometryIndex < 0)\n        geometryIndex = coords.length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[geometryIndex].length + segmentIndex - 1;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(\n        [\n          coords[geometryIndex][segmentIndex],\n          coords[geometryIndex][segmentIndex + 1]\n        ],\n        properties,\n        options\n      );\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[multiFeatureIndex].length + segmentIndex - 1;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(\n        [\n          coords[multiFeatureIndex][segmentIndex],\n          coords[multiFeatureIndex][segmentIndex + 1]\n        ],\n        properties,\n        options\n      );\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (segmentIndex < 0)\n        segmentIndex = coords[multiFeatureIndex][geometryIndex].length - segmentIndex - 1;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.lineString)(\n        [\n          coords[multiFeatureIndex][geometryIndex][segmentIndex],\n          coords[multiFeatureIndex][geometryIndex][segmentIndex + 1]\n        ],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\nfunction findPoint(geojson, options) {\n  options = options || {};\n  if (!(0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.isObject)(options))\n    throw new Error(\"options is invalid\");\n  var featureIndex = options.featureIndex || 0;\n  var multiFeatureIndex = options.multiFeatureIndex || 0;\n  var geometryIndex = options.geometryIndex || 0;\n  var coordIndex = options.coordIndex || 0;\n  var properties = options.properties;\n  var geometry;\n  switch (geojson.type) {\n    case \"FeatureCollection\":\n      if (featureIndex < 0)\n        featureIndex = geojson.features.length + featureIndex;\n      properties = properties || geojson.features[featureIndex].properties;\n      geometry = geojson.features[featureIndex].geometry;\n      break;\n    case \"Feature\":\n      properties = properties || geojson.properties;\n      geometry = geojson.geometry;\n      break;\n    case \"Point\":\n    case \"MultiPoint\":\n      return null;\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n      geometry = geojson;\n      break;\n    default:\n      throw new Error(\"geojson is invalid\");\n  }\n  if (geometry === null)\n    return null;\n  var coords = geometry.coordinates;\n  switch (geometry.type) {\n    case \"Point\":\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(coords, properties, options);\n    case \"MultiPoint\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(coords[multiFeatureIndex], properties, options);\n    case \"LineString\":\n      if (coordIndex < 0)\n        coordIndex = coords.length + coordIndex;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(coords[coordIndex], properties, options);\n    case \"Polygon\":\n      if (geometryIndex < 0)\n        geometryIndex = coords.length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[geometryIndex].length + coordIndex;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(coords[geometryIndex][coordIndex], properties, options);\n    case \"MultiLineString\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[multiFeatureIndex].length + coordIndex;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(coords[multiFeatureIndex][coordIndex], properties, options);\n    case \"MultiPolygon\":\n      if (multiFeatureIndex < 0)\n        multiFeatureIndex = coords.length + multiFeatureIndex;\n      if (geometryIndex < 0)\n        geometryIndex = coords[multiFeatureIndex].length + geometryIndex;\n      if (coordIndex < 0)\n        coordIndex = coords[multiFeatureIndex][geometryIndex].length - coordIndex;\n      return (0,_turf_helpers__WEBPACK_IMPORTED_MODULE_0__.point)(\n        coords[multiFeatureIndex][geometryIndex][coordIndex],\n        properties,\n        options\n      );\n  }\n  throw new Error(\"geojson is invalid\");\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@turf/meta/dist/esm/index.js\n");

/***/ })

};
;